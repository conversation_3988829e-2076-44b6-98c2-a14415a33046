/**
 * Configuration for background templates categories and styles
 * Maps to webp preview images in /public/assets/background-templates/
 */

/**
 * Unsplash placeholder images for background styles and categories
 * Used when a style's preview image is not available or fails to load
 * Organized by style ID for more granular control
 */
export const UNSPLASH_PLACEHOLDERS = {
  // Category defaults
  cinematic: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=600&q=80',
  gaming: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?auto=format&fit=crop&w=600&q=80',
  tech: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=600&q=80',
  business: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80',
  abstract: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=600&q=80',
  solid: 'https://images.unsplash.com/photo-1502082553048-f009c37129b9?auto=format&fit=crop&w=600&q=80',
  
  // Cinematic style-specific placeholders
  cinematic_bokeh: 'https://images.unsplash.com/photo-1512790182412-b19e6d62bc39?auto=format&fit=crop&w=600&q=80',
  cinematic_flare: 'https://images.unsplash.com/photo-1611523524760-3249121f3364?auto=format&fit=crop&w=600&q=80',
  cinematic_moody: 'https://images.unsplash.com/photo-1535016120720-40c646be5580?auto=format&fit=crop&w=600&q=80',
  cinematic_spotlight: 'https://images.unsplash.com/photo-1560472608-46c12eb3d8db?auto=format&fit=crop&w=600&q=80',
  cinematic_anamorphic: 'https://images.unsplash.com/photo-1598899134739-24c46f58b8c0?auto=format&fit=crop&w=600&q=80',
  cinematic_silhouette: 'https://images.unsplash.com/photo-1519462568576-0c890db8bdc4?auto=format&fit=crop&w=600&q=80',
  cinematic_noir: 'https://images.unsplash.com/photo-1501084291732-13b1ba8f0ebc?auto=format&fit=crop&w=600&q=80',
  
  // Gaming style-specific placeholders
  gaming_futuristic: 'https://images.unsplash.com/photo-1550745165-9bc0b252726f?auto=format&fit=crop&w=600&q=80',
  gaming_neon: 'https://images.unsplash.com/photo-1550439062-609e1531270e?auto=format&fit=crop&w=600&q=80',
  gaming_battle: 'https://images.unsplash.com/photo-1626862031093-76234c9bdb7e?auto=format&fit=crop&w=600&q=80',
  gaming_pixel: 'https://images.unsplash.com/photo-1550063873-ab792950096b?auto=format&fit=crop&w=600&q=80',
  gaming_esports: 'https://images.unsplash.com/photo-1627856014029-a8f3dac4eb04?auto=format&fit=crop&w=600&q=80',
  gaming_rpg: 'https://images.unsplash.com/photo-1518709766631-a6a7f45921c3?auto=format&fit=crop&w=600&q=80',
  gaming_console: 'https://images.unsplash.com/photo-1592155931584-901ac15763e3?auto=format&fit=crop&w=600&q=80',
  
  // Tech style-specific placeholders
  tech_matrix: 'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?auto=format&fit=crop&w=600&q=80',
  tech_circuit: 'https://images.unsplash.com/photo-1518770660439-4636190af475?auto=format&fit=crop&w=600&q=80',
  tech_minimal: 'https://images.unsplash.com/photo-1530893609608-32a9af3aa95c?auto=format&fit=crop&w=600&q=80',
  tech_device: 'https://images.unsplash.com/photo-1516387938699-a93567ec168e?auto=format&fit=crop&w=600&q=80',
  tech_data: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&w=600&q=80',
  tech_gradient: 'https://images.unsplash.com/photo-1557682250-d6eba2b0a9db?auto=format&fit=crop&w=600&q=80',
  tech_blueprint: 'https://images.unsplash.com/photo-1581089781785-603411fa4887?auto=format&fit=crop&w=600&q=80',
  
  // Business style-specific placeholders
  business_corporate: 'https://images.unsplash.com/photo-1497215842964-222b430dc094?auto=format&fit=crop&w=600&q=80',
  business_presentation: 'https://images.unsplash.com/photo-1573164713988-8665fc963095?auto=format&fit=crop&w=600&q=80',
  business_finance: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?auto=format&fit=crop&w=600&q=80',
  business_office: 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?auto=format&fit=crop&w=600&q=80',
  business_minimal: 'https://images.unsplash.com/photo-1497215842964-222b430dc094?auto=format&fit=crop&w=600&q=80',
  business_startup: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?auto=format&fit=crop&w=600&q=80',
  business_global: 'https://images.unsplash.com/photo-1526304640581-d334cdbbf45e?auto=format&fit=crop&w=600&q=80',
  
  // Abstract style-specific placeholders
  abstract_wave: 'https://images.unsplash.com/photo-1553356084-58ef4a67b2a7?auto=format&fit=crop&w=600&q=80',
  abstract_geometric: 'https://images.unsplash.com/photo-1544616326-a118375df80a?auto=format&fit=crop&w=600&q=80',
  abstract_particles: 'https://images.unsplash.com/photo-1633626738832-e148adcef572?auto=format&fit=crop&w=600&q=80',
  abstract_gradient: 'https://images.unsplash.com/photo-1557682250-d6eba2b0a9db?auto=format&fit=crop&w=600&q=80',
  abstract_minimal: 'https://images.unsplash.com/photo-1550440869-dcbe5a9cfe65?auto=format&fit=crop&w=600&q=80',
  abstract_paint: 'https://images.unsplash.com/photo-1533158307587-828f0a76ef46?auto=format&fit=crop&w=600&q=80',
  abstract_liquid: 'https://images.unsplash.com/photo-1604076913837-52ab5629fba9?auto=format&fit=crop&w=600&q=80',
  
  // Solid color style-specific placeholders (more subtle variations)
  solid_black: 'https://images.unsplash.com/photo-1550684376-efcbd6e3f031?auto=format&fit=crop&w=600&q=80',
  solid_white: 'https://images.unsplash.com/photo-1588345921523-c2dcdb7f1dcd?auto=format&fit=crop&w=600&q=80',
  solid_red: 'https://images.unsplash.com/photo-1530122037265-a5f1f91d3b99?auto=format&fit=crop&w=600&q=80',
  solid_blue: 'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?auto=format&fit=crop&w=600&q=80',
  solid_green: 'https://images.unsplash.com/photo-1497250681960-ef046c08a56e?auto=format&fit=crop&w=600&q=80',
  solid_purple: 'https://images.unsplash.com/photo-1564951434112-64d74cc2a2d7?auto=format&fit=crop&w=600&q=80',
  solid_custom: 'https://images.unsplash.com/photo-1557683316-973673baf926?auto=format&fit=crop&w=600&q=80',
  
  // Default fallback if category or style is not in this mapping
  default: 'https://images.unsplash.com/photo-1579547945413-497e1b99dac0?auto=format&fit=crop&w=600&q=80'
};

// Categories for background templates
export const BACKGROUND_CATEGORIES = [
  {
    id: 'cinematic',
    name: 'Cinematic',
    icon: 'solar:video-frame-bold-duotone',
    styles: [
      { 
        id: 'cinematic_bokeh', 
        name: 'Bokeh Bliss', 
        preview: '/assets/background-templates/cinematic/bokeh.webp',
        description: 'A soft, cinematic bokeh effect with warm glowing orbs perfect for creating a dreamy, premium atmosphere.',
        icon: 'solar:camera-diaphragm-bold-duotone'
      },
      { 
        id: 'cinematic_flare', 
        name: 'Lens Flare', 
        preview: '/assets/background-templates/cinematic/flare.webp',
        description: 'Dynamic lens flare effect with striking light rays that add visual drama and a professional film look.',
        icon: 'solar:flash-bold-duotone'
      },
      { 
        id: 'cinematic_moody', 
        name: 'Moody Gradient', 
        preview: '/assets/background-templates/cinematic/moody.webp',
        description: 'Deep atmospheric gradient with subtle smoky textures for serious, thoughtful content.',
        icon: 'solar:mask-sad-bold-duotone'
      },
      { 
        id: 'cinematic_spotlight', 
        name: 'Spotlight', 
        preview: '/assets/background-templates/cinematic/spotlight.webp',
        description: 'Dramatic spotlight effect against a dark background, perfect for highlighting key subjects or concepts.',
        icon: 'solar:lamp-bold-duotone'
      },
      { 
        id: 'cinematic_anamorphic', 
        name: 'Anamorphic', 
        preview: '/assets/background-templates/cinematic/anamorphic.webp',
        description: 'Classic anamorphic lens blue streaks against a dark background for a premium Hollywood feel.',
        icon: 'solar:screen-camera-bold-duotone'
      },
      { 
        id: 'cinematic_silhouette', 
        name: 'Silhouette Sunset', 
        preview: '/assets/background-templates/cinematic/silhouette.webp',
        description: 'Dramatic backlit silhouette style against a colorful sunset gradient.',
        icon: 'solar:mountains-sunset-bold-duotone'
      },
      { 
        id: 'cinematic_noir', 
        name: 'Film Noir', 
        preview: '/assets/background-templates/cinematic/noir.webp',
        description: 'High-contrast black and white with dramatic shadows and light patterns.',
        icon: 'solar:magic-stick-3-bold-duotone'
      }
    ]
  },
  {
    id: 'gaming',
    name: 'Gaming',
    icon: 'solar:gamepad-minimalistic-bold-duotone',
    styles: [
      { 
        id: 'gaming_futuristic', 
        name: 'Futuristic HUD', 
        preview: '/assets/background-templates/gaming/futuristic.webp',
        description: 'Digital HUD elements with glowing UI components perfect for tech or gaming content.' 
      },
      { 
        id: 'gaming_neon', 
        name: 'Neon Arcade', 
        preview: '/assets/background-templates/gaming/neon.webp',
        description: 'Vibrant neon colors with retro arcade styling and grid patterns.' 
      },
      { 
        id: 'gaming_battle', 
        name: 'Battle Arena', 
        preview: '/assets/background-templates/gaming/battle.webp',
        description: 'Dynamic action-oriented background with energy effects and explosive elements.' 
      },
      { 
        id: 'gaming_pixel', 
        name: 'Pixel Art', 
        preview: '/assets/background-templates/gaming/pixel.webp',
        description: 'Retro pixel art styling with blocky elements and 8-bit aesthetic.' 
      },
      { 
        id: 'gaming_esports', 
        name: 'E-Sports', 
        preview: '/assets/background-templates/gaming/esports.webp',
        description: 'Professional e-sports styling with angular elements and team colors.' 
      },
      { 
        id: 'gaming_rpg', 
        name: 'Fantasy RPG', 
        preview: '/assets/background-templates/gaming/rpg.webp',
        description: 'Magical fantasy environment with mystical elements and atmospheric lighting.' 
      },
      { 
        id: 'gaming_console', 
        name: 'Console Vibes', 
        preview: '/assets/background-templates/gaming/console.webp',
        description: 'Contemporary gaming console aesthetic with controller elements and brand colors.' 
      }
    ]
  },
  {
    id: 'tech',
    name: 'Tech',
    icon: 'solar:cpu-bold-duotone',
    styles: [
      { 
        id: 'tech_matrix', 
        name: 'Digital Matrix', 
        preview: '/assets/background-templates/tech/matrix.webp',
        description: 'Streaming code and data visualization elements for a cyber tech feel.',
        icon: 'solar:code-bold-duotone'
      },
      { 
        id: 'tech_circuit', 
        name: 'Circuit Board', 
        preview: '/assets/background-templates/tech/circuit.webp',
        description: 'Detailed circuit board patterns with glowing traces and tech elements.',
        icon: 'solar:cpu-bolt-bold-duotone'
      },
      { 
        id: 'tech_minimal', 
        name: 'Minimal Tech', 
        preview: '/assets/background-templates/tech/minimal.webp',
        description: 'Clean, minimalist tech aesthetic with subtle geometric elements.',
        icon: 'solar:minimalistic-bold-duotone'
      },
      { 
        id: 'tech_device', 
        name: 'Device Showcase', 
        preview: '/assets/background-templates/tech/device.webp',
        description: 'Modern device-oriented background with clean surfaces and reflections.',
        icon: 'solar:smartphone-bold-duotone'
      },
      { 
        id: 'tech_data', 
        name: 'Data Visualization', 
        preview: '/assets/background-templates/tech/data.webp',
        description: 'Abstract data charts, graphs and analytics visuals on a dark backdrop.',
        icon: 'solar:chart-bold-duotone'
      },
      { 
        id: 'tech_gradient', 
        name: 'Tech Gradient', 
        preview: '/assets/background-templates/tech/gradient.webp',
        description: 'Smooth modern gradient with subtle tech-inspired elements.',
        icon: 'solar:square-top-line-bold-duotone'
      },
      { 
        id: 'tech_blueprint', 
        name: 'Blueprint', 
        preview: '/assets/background-templates/tech/blueprint.webp',
        description: 'Technical blueprint style with schematic drawings on dark blue background.',
        icon: 'solar:document-bold-duotone'
      }
    ]
  },
  {
    id: 'business',
    name: 'Business',
    icon: 'solar:case-minimalistic-bold-duotone',
    styles: [
      { 
        id: 'business_corporate', 
        name: 'Corporate', 
        preview: '/assets/background-templates/business/corporate.webp',
        description: 'Professional corporate styling with clean lines and business-appropriate elements.' 
      },
      { 
        id: 'business_presentation', 
        name: 'Presentation', 
        preview: '/assets/background-templates/business/presentation.webp',
        description: 'Slide-deck inspired background with areas for key points and data.' 
      },
      { 
        id: 'business_finance', 
        name: 'Finance', 
        preview: '/assets/background-templates/business/finance.webp',
        description: 'Financial data visualization with stock charts and business metrics.' 
      },
      { 
        id: 'business_office', 
        name: 'Modern Office', 
        preview: '/assets/background-templates/business/office.webp',
        description: 'Contemporary office environment with clean, professional aesthetic.' 
      },
      { 
        id: 'business_minimal', 
        name: 'Minimal Business', 
        preview: '/assets/background-templates/business/minimal.webp',
        description: 'Understated professional background with subtle geometric elements.' 
      },
      { 
        id: 'business_startup', 
        name: 'Startup Vibe', 
        preview: '/assets/background-templates/business/startup.webp',
        description: 'Dynamic, creative startup atmosphere with modern design elements.' 
      },
      { 
        id: 'business_global', 
        name: 'Global Business', 
        preview: '/assets/background-templates/business/global.webp',
        description: 'Worldwide business theme with map elements and global connectivity visuals.' 
      }
    ]
  },
  {
    id: 'abstract',
    name: 'Abstract',
    icon: 'solar:square-academic-cap-bold-duotone',
    styles: [
      { 
        id: 'abstract_wave', 
        name: 'Flowing Waves', 
        preview: '/assets/background-templates/abstract/wave.webp',
        description: 'Smooth, flowing wave patterns with dynamic curves and gradients.' 
      },
      { 
        id: 'abstract_geometric', 
        name: 'Geometric', 
        preview: '/assets/background-templates/abstract/geometric.webp',
        description: 'Bold geometric shapes and patterns with modern color schemes.' 
      },
      { 
        id: 'abstract_particles', 
        name: 'Particle Flow', 
        preview: '/assets/background-templates/abstract/particles.webp',
        description: 'Dynamic particle systems creating flowing, organic patterns.' 
      },
      { 
        id: 'abstract_gradient', 
        name: 'Gradient Mesh', 
        preview: '/assets/background-templates/abstract/gradient.webp',
        description: 'Smooth, colorful gradient meshes with modern color transitions.' 
      },
      { 
        id: 'abstract_minimal', 
        name: 'Minimal Abstract', 
        preview: '/assets/background-templates/abstract/minimal.webp',
        description: 'Clean, minimalist abstract design with simple shapes and limited colors.' 
      },
      { 
        id: 'abstract_paint', 
        name: 'Paint Texture', 
        preview: '/assets/background-templates/abstract/paint.webp',
        description: 'Artistic paint textures and brush strokes with organic feeling.' 
      },
      { 
        id: 'abstract_liquid', 
        name: 'Liquid Form', 
        preview: '/assets/background-templates/abstract/liquid.webp',
        description: 'Fluid, liquid abstract shapes with smooth, glossy finish.' 
      }
    ]
  },
  {
    id: 'solid',
    name: 'Solid Color',
    icon: 'solar:palette-bold-duotone',
    styles: [
      { 
        id: 'solid_black', 
        name: 'Black', 
        preview: '/assets/background-templates/solid/black.webp',
        description: 'Deep black background with subtle vignette for elegant styling.',
        color: '#000000' 
      },
      { 
        id: 'solid_white', 
        name: 'White', 
        preview: '/assets/background-templates/solid/white.webp',
        description: 'Clean white background with subtle texture for a fresh, minimal look.',
        color: '#FFFFFF' 
      },
      { 
        id: 'solid_red', 
        name: 'Red', 
        preview: '/assets/background-templates/solid/red.webp',
        description: 'Bold red background with subtle gradient for eye-catching impact.',
        color: '#FF0000' 
      },
      { 
        id: 'solid_blue', 
        name: 'Blue', 
        preview: '/assets/background-templates/solid/blue.webp',
        description: 'Professional blue background with subtle depth variation.',
        color: '#0000FF' 
      },
      { 
        id: 'solid_green', 
        name: 'Green', 
        preview: '/assets/background-templates/solid/green.webp',
        description: 'Vibrant green background with subtle texture for natural themes.',
        color: '#00FF00' 
      },
      { 
        id: 'solid_purple', 
        name: 'Purple', 
        preview: '/assets/background-templates/solid/purple.webp',
        description: 'Rich purple background with subtle gradient for creative, premium feel.',
        color: '#800080' 
      },
      { 
        id: 'solid_custom', 
        name: 'Custom Color', 
        preview: '/assets/background-templates/solid/custom.webp',
        description: 'Choose your own custom solid color background.',
        isCustom: true 
      }
    ]
  }
];

// Helper function to find a background style by ID
export const findBackgroundStyleById = (styleId) => {
  for (const category of BACKGROUND_CATEGORIES) {
    const style = category.styles.find(style => style.id === styleId);
    if (style) {
      return { category, style };
    }
  }
  return null;
}; 