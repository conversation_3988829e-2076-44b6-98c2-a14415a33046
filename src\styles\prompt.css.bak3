/* Styles specific to the prompt input component */

.prompt-section {
  transition: all 0.3s ease;
}

.prompt-label {
  transition: all 0.2s ease;
}

/* Status badges */
.prompt-enhancing-badge,
.prompt-auto-enhanced-badge {
  transition: all 0.2s ease;
}

/* Suggestion chips */
.prompt-suggestion-chip {
  transition: all 0.2s ease;
}

.prompt-suggestion-chip:hover {
  transform: translateY(-1px);
}

.prompt-suggestion-chip:active {
  transform: translateY(0);
}

/* Preview block */
.prompt-preview-block {
  transition: all 0.2s ease;
}

/* Animation for the enhancing spinner */
@keyframes spinner {
  to {transform: rotate(360deg);}
}

.animate-spin {
  animation: spinner 1s linear infinite;
}

/* Locked state styling */
.prompt-lock-indicator {
  transition: all 0.2s ease;
}

.prompt-lock-tooltip {
  transition: opacity 0.2s ease, visibility 0.2s ease;
  pointer-events: none;
}

/* Style for improve prompt button */
.prompt-improve-btn {
  transition: all 0.2s ease;
}

.prompt-improve-btn:hover {
  transform: translateY(-1px);
}

.prompt-improve-btn:active {
  transform: translateY(0);
}

/* Line clamp for text previews */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Character counter */
.prompt-char-count {
  transition: color 0.2s ease;
}

.prompt-char-count.warning {
  color: #F59E0B; /* amber-500 */
}

.prompt-char-count.error {
  color: #EF4444; /* red-500 */
}

/* Enhanced Prompt Input Styling for Cinematic Typewriter Effect */

.prompt-input-container {
    width: 100%;
    max-width: 100%;
    position: relative;
}

.prompt-input-wrapper {
    position: relative;
    width: 100%;
    max-width: 100%;
}

.prompt-textarea {
    width: 100%;
    min-height: 140px;
    background-color: #374151; /* bg-gray-700 */
    border: 1px solid #4B5563; /* border-gray-600 */
    border-radius: 8px;
    padding: 1.25rem;
    color: #F9FAFB; /* text-gray-50 */
    font-size: 0.9rem;
    line-height: 1.5;
    resize: vertical;
    transition: all 0.3s ease-in-out;
    font-family: inherit;
    letter-spacing: normal;
    
    /* Enhanced focus and hover states */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Typewriter mode styling */
.prompt-textarea.font-mono {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
    letter-spacing: 0.025em;
    line-height: 1.6;
    background-color: #2D3748; /* Slightly darker for terminal feel */
    border-color: #553C9A; /* Purple tint */
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.1);
}

/* Enhanced focus state */
.prompt-textarea:focus {
    outline: none;
    border-color: #8B5CF6; /* border-purple-500 */
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.15);
    
}

/* Typewriter focus state */
.prompt-textarea.font-mono:focus {
    border-color: #A855F7;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2), 0 0 25px rgba(139, 92, 246, 0.15);
    
}

/* Placeholder styling for typewriter mode */
.prompt-textarea.font-mono::placeholder {
    color: #9CA3AF;
    opacity: 0.8;
    font-weight: 400;
}

/* Smooth transitions for font changes */
.prompt-textarea {
    transition: 
        font-family 0.3s ease-in-out,
        letter-spacing 0.3s ease-in-out,
        background-color 0.3s ease-in-out,
        border-color 0.3s ease-in-out,
        box-shadow 0.3s ease-in-out,
        min-height 0.3s ease-in-out;
}

/* Responsive width adjustments */
@media (min-width: 768px) {
    .prompt-input-container {
        max-width: 100%;
    }
    
    .prompt-textarea {
        font-size: 1rem;
        padding: 1.5rem;
    }
    
    .prompt-textarea.font-mono {
        font-size: 0.95rem;
        padding: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .prompt-input-container {
        max-width: 100%;
    }
    
    .prompt-textarea {
        min-height: 160px;
        font-size: 1rem;
    }
    
    .prompt-textarea.font-mono {
        min-height: 140px;
        font-size: 1rem;
    }
}

/* Cinematic glow animation */
@keyframes pulse {
    0%, 100% {
        opacity: 0.4;
    }
    50% {
        opacity: 0.8;
    }
}

/* Improve button enhanced styling - static version without animations */
.prompt-input-wrapper button[aria-label*="Improve"] {
    backdrop-filter: blur(8px);
    background-color: rgba(55, 65, 81, 0.8);
    border-radius: 6px;
    padding: 0.5rem;
    transition: none; /* Remove all transitions */
    font-size: 0.88rem;
}

/* Improve button icon sizing - static */
.prompt-input-wrapper button[aria-label*="Improve"] .iconify {
    width: 15.4px !important;
    height: 15.4px !important;
    transition: none; /* Remove icon transitions */
}

/* Remove all hover and active states for improve button */
.prompt-input-wrapper button[aria-label*="Improve"]:hover {
    /* No hover effects */
}

.prompt-input-wrapper button[aria-label*="Improve"]:hover .iconify {
    /* No icon hover effects */
}

.prompt-input-wrapper button[aria-label*="Improve"]:active {
    /* No active effects */
}

/* Lock tooltip enhanced styling */
#prompt-lock-tooltip {
    backdrop-filter: blur(12px);
    background-color: rgba(245, 158, 11, 0.95);
    border: 1px solid rgba(245, 158, 11, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Disabled state styling */
.prompt-textarea:disabled {
    background-color: #2D3748;
    border-color: #4A5568;
    color: #A0AEC0;
    cursor: not-allowed;
}

/* Selection styling for better UX */
.prompt-textarea::selection {
    background-color: rgba(139, 92, 246, 0.3);
    color: #F9FAFB;
}

/* Typewriter effect for improve button output */
.prompt-textarea.improving-typewriter {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
    letter-spacing: 0.025em;
    line-height: 1.6;
     /* Darker background during typing */
    border-color: #A855F7; /* Purple border during improvement */
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2), 0 0 25px rgba(139, 92, 246, 0.15);
    transition: all 0.3s ease-in-out;
}

/* Subtle glow animation during typewriter effect */
.prompt-textarea.improving-typewriter {
    animation: subtle-pulse 2s ease-in-out infinite;
}

@keyframes subtle-pulse {
    0%, 100% {
        box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.15), 0 0 20px rgba(139, 92, 246, 0.1);
    }
    50% {
        box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.25), 0 0 30px rgba(139, 92, 246, 0.2);
    }
} 