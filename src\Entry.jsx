// Import React from the installed packages
const React = window.React;
const { useState, useEffect } = React;
const ReactDOM = window.ReactDOM;

// Import components
import { App } from './App.jsx';
import Welcome from './pages/Welcome.jsx';
import { supabase } from './utils/supabaseClient.js';

/**
 * Entry Component - Handles routing between Welcome and App
 * 
 * This component decides whether to show the Welcome (auth) screen
 * or the main App based on Supabase authentication state.
 */
const Entry = () => {
    // State to track if user is authenticated
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    // State to track if we've checked auth (prevents flash of welcome screen)
    const [hasCheckedAuth, setHasCheckedAuth] = useState(false);
    // State to track loading
    const [isLoading, setIsLoading] = useState(true);
    
    // Check for authentication on load
    useEffect(() => {
        checkUser();
        
        // Listen for auth state changes
        const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
            setIsAuthenticated(!!session);
            setIsLoading(false);
        });
        
        return () => {
            subscription.unsubscribe();
        };
    }, []);
    
    // Check if user is logged in
    const checkUser = async () => {
        try {
            const { data: { session } } = await supabase.auth.getSession();
            setIsAuthenticated(!!session);
            setHasCheckedAuth(true);
            setIsLoading(false);
        } catch (error) {
            console.error('Error checking auth:', error);
            setHasCheckedAuth(true);
            setIsLoading(false);
        }
    };
    
    // Event handler for when user authenticates
    const handleAuthentication = () => {
        setIsAuthenticated(true);
    };
    
    // If we're still checking auth, show a loading screen
    if (!hasCheckedAuth || isLoading) {
        return React.createElement('div', {
            className: 'min-h-screen bg-gray-900 flex items-center justify-center'
        },
            React.createElement('div', {
                className: 'text-center'
            },
                React.createElement('div', {
                    className: 'inline-flex items-center justify-center w-16 h-16 mb-4'
                },
                    React.createElement('svg', {
                        className: 'animate-spin h-12 w-12 text-purple-500',
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24'
                    },
                        React.createElement('circle', {
                            className: 'opacity-25',
                            cx: '12',
                            cy: '12',
                            r: '10',
                            stroke: 'currentColor',
                            strokeWidth: '4'
                        }),
                        React.createElement('path', {
                            className: 'opacity-75',
                            fill: 'currentColor',
                            d: 'M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                        })
                    )
                ),
                React.createElement('p', {
                    className: 'text-gray-400'
                }, 'Loading...')
            )
        );
    }
    
    // Render either Welcome or App based on authentication state
    return isAuthenticated 
        ? React.createElement(App)
        : React.createElement(Welcome, { onAuthenticated: handleAuthentication });
};

// Render the Entry component to the DOM
const container = document.getElementById('root');
if (container) {
    const root = ReactDOM.createRoot(container);
    root.render(React.createElement(Entry));
} else {
    console.error("Root element not found");
}

export default Entry; 