import { detectBrandLogos, hasGamingBrand } from './brandLogos.js'

export const buildThumbnailPrompt = (details) => {
    const {
        initialPrompt,
        includePerson,
        mood,
        includeIcons,
        textOverlayEnabled,
        overlayText, // Custom text from user
        textPosition, // Text position (e.g., 'top-right')
        fitFullCanvas, // Fit full canvas toggle
        selectedTextSize, // Added: 'Medium' or 'Large'
    } = details;

    // --- Base Prompt Structure ---
    let textBlock = '';

    if (textOverlayEnabled && overlayText) {
        textBlock = `
Text Overlay:
- Add a bold, uppercase title related to the video topic: "${overlayText}"
- Use this exact text. Do not add words from the main prompt.
- Arrange text in a pyramid layout if multiple lines are needed.
`;
    } else if (textOverlayEnabled && initialPrompt) {
        // Use initial prompt for text, keep it short (3-6 words), avoid ellipses
        textBlock = `
Text Overlay:
- Add a bold, uppercase title related to the video topic: "${initialPrompt}"
- Keep the title concise (3-6 words maximum). Do not use ellipses (...).
`;
    } else {
        // Explicitly state no text if disabled
        textBlock = `
Text Overlay:
- Do NOT include any text overlay.
`;
    }

    // Add Text Styling Rules ONLY if text is enabled
    if (textOverlayEnabled) {
        const sizeInstruction = selectedTextSize === 'Medium'
            ? `Use moderately sized title text. The text should be clearly readable but must NOT dominate the thumbnail. The text should take up about 20-30% of the image height, leaving plenty of space for the main subject and background. Avoid making the text too bold or oversized.`
            : `Use extremely large, bold, attention-grabbing title text. The text should be the most prominent element, taking up 40-60% of the image height. Let the text overlap or interact with the subject if needed, and make sure it is highly visible even on small mobile screens. Prioritize maximum impact and click appeal.`;

        textBlock += `
- Font: modern sans-serif, bold and legible.
- ${sizeInstruction} <!-- Text Size Control -->
- For "Large" text, make the title the main focus, even if it partially covers the subject.
- For "Medium" text, keep the title balanced with the rest of the design.
- Colors: high-contrast (e.g., yellow/white text on dark background, or red/black text on light background).
- Style: Add a STRONG, vibrant GLOW effect and a noticeable DROP SHADOW to all text to increase click appeal and make it CRISPY.
- Placement: ${textPosition || 'center-right'}. Ensure text is placed clearly within the frame.
- Safe Zone: Ensure text has **at least 100px margin** from all canvas edges (top, bottom, left, right) to prevent clipping on YouTube mobile/desktop views. If text is too long for the safe zone, resize or wrap it creatively to fit within the 100px boundary.
`;
    }

    // --- Visual Focus & Composition ---
    // ... existing code ...

    return textBlock;
}

// AUTO GAME THUMBNAIL ENHANCER (applies @auto-game-thumbnails rule)
function autoEnhanceGamePrompt(originalPrompt, overlayOn = true, detectedLogos = []) {
    // Early-out if no prompt provided
    if (!originalPrompt) return originalPrompt;

    // --- MODIFIED: Only apply game enhancement if a gaming brand is explicitly detected ---
    if (!hasGamingBrand(detectedLogos)) {
        // If no gaming brands are in the prompt (e.g., "Bolt.new vs Framer"),
        // do not apply game-specific styling. Return original prompt.
        return originalPrompt;
    }
    // --- END MODIFICATION ---

    const p = originalPrompt.trim();
    const lower = p.toLowerCase();

    // --- 1. Identify if the prompt is about a supported FPS/Battle-Royale game
    const gameKeywords = [
        'fortnite', 'warzone', 'call of duty', 'cod', 'valorant', 'pubg',
        'apex', 'apex legends', 'cs2', 'counter strike', 'cs:go', 'minecraft'
    ];
    const compareKeywords = [' vs ', ' 1v1 ', ' noob vs pro', ' showdown', ' pro vs noob'];

    // This check is now partly redundant due to hasGamingBrand, but good for narrowing down to specific game styling.
    const containsGameKeyword = gameKeywords.some(g => lower.includes(g));
    if (!containsGameKeyword) return originalPrompt; // Fallback if somehow gaming brand detected but no keyword here

    const containsCompare = compareKeywords.some(k => lower.includes(k));

    // --- 2. Resolve canonical game key
    let gameKey = gameKeywords.find(g => lower.includes(g)) || 'the game';
    // Normalise some aliases
    if (gameKey === 'cod') gameKey = 'call of duty';
    if (gameKey === 'apex legends') gameKey = 'apex';
    if (gameKey === 'cs:go') gameKey = 'cs2'; // treat CS2/CS:GO similarly for now
    if (gameKey === 'counter strike') gameKey = 'cs2';

    // --- 3. Meta-data lookup for iconic characters & maps
    const gameMeta = {
        'fortnite': {
            char: 'Renegade Raider or Drift skin',
            map: 'Tilted Towers (OG map)',
            logo: 'Fortnite'
        },
        'warzone': {
            char: 'Ghost Operator or Captain Price',
            map: 'Verdansk',
            logo: 'Call of Duty Warzone'
        },
        'call of duty': {
            char: 'Soap MacTavish or Ghost',
            map: 'Nuketown',
            logo: 'Call of Duty'
        },
        'valorant': {
            char: 'Jett or Phoenix agent',
            map: 'Bind',
            logo: 'Valorant'
        },
        'pubg': {
            char: 'Survivor wearing Level 3 Helmet',
            map: 'Erangel',
            logo: 'PUBG'
        },
        'apex': {
            char: 'Wraith or Octane legend',
            map: 'Kings Canyon',
            logo: 'Apex Legends'
        },
        'cs2': {
            char: 'Counter-Terrorist with AWP',
            map: 'Dust II',
            logo: 'Counter-Strike 2'
        }
    };

    const meta = gameMeta[gameKey] || {
        char: 'a top character',
        map: 'an iconic map location',
        logo: gameKey
    };

    // --- 4. Layout variation to reduce repetition
    const layouts = [
        'split-screen face-off',
        'mirrored action pose',
        'side-by-side with dramatic dutch-angle',
        'one character in foreground, the rival in background with depth-of-field blur'
    ];
    // Use simple RNG for layout variety
    const layout = layouts[Math.floor(Math.random() * layouts.length)];

    // --- 5. Overlay logic
    let overlayClause;
    if (overlayOn) {
        const overlayTextSuggested = p.toUpperCase();
        overlayClause = ` Add bold, uppercase, glowing high-contrast text overlay (e.g., \"${overlayTextSuggested}\") in a modern font, positioned for maximum impact.`;
    } else {
        overlayClause = ' Do NOT include any text overlay.';
    }

    // --- 6. Build enhanced prompt
    const versusNote = containsCompare
        ? ' Depict a dramatic versus/comparison scenario with clear tension between the two sides.'
        : '';

    return `Create a cinematic YouTube thumbnail at 1280x720 featuring instantly recognizable, official in-game characters from ${gameKey} (e.g., ${meta.char}) in a ${layout}.${versusNote} Characters must use game-authentic gear, facial expressions, and art style. Background: show ${meta.map}, rendered with faithful textures, lighting, and props from the real game. Integrate the official ${meta.logo} logo subtly into the composition. Ensure art style, color palette, and overall look precisely match ${gameKey}. Avoid using generic avatars or repeating the same background/character arrangement as previous thumbnails.${overlayClause}`;
}

/**
 * Builds a detailed prompt for GPT-image-1 YouTube thumbnail generation,
 * including face reference matching and all user controls.
 *
 * @param {Object} options
 * @param {string} options.userPrompt - The main topic or idea from the user.
 * @param {boolean} options.includePerson - Whether to include a person.
 * @param {string} options.mood - Mood for the person (e.g., "Excited", "Serious").
 * @param {boolean} options.includeIcons - Whether to include icons.
 * @param {boolean} options.textOverlay - Whether to add text overlay.
 * @param {string} options.overlayText - Custom text for overlay (if any).
 * @param {string} options.overlayPosition - Where to place the overlay text.
 * @param {string} options.faceDescription - Description of the reference face (if any).
 * @returns {string} The full prompt for the image generation API.
 */
export function buildPrompt({
  userPrompt,
  includePerson,
  mood,
  includeIcons,
  textOverlay,
  selectedExpression,
  faceDescription,
  overlayText,
  overlayPosition,
  selectedTextSize,
  selectedFontFamily,
  primaryTextColor,
  secondaryTextColor,
  fitFullCanvas,
  selectedGender,
  selectedBackgroundType,
  selectedBackgroundStyleId,
  selectedSolidBgColor,
  selectedColorGrade
}) {
    // Apply auto-game-thumbnails enhancement, now aware of brand category
    userPrompt = autoEnhanceGamePrompt(userPrompt, textOverlay);

    // --- Brand Logo Detection (for logo embedding feature) ---
    let detectedBrandLogos = [];
    if (includeIcons) {
        // detectBrandLogos now returns an array of objects: { name: string, category: string }
        detectedBrandLogos = detectBrandLogos(userPrompt);
    }

    // --- Improved Clean Background Mode ---
    if (!textOverlay && !includePerson && !includeIcons) {
        let cleanPrompt = `Create a cinematic YouTube thumbnail image at 1280x720 resolution for a background ONLY.\n\n`;
        cleanPrompt += `Background Style & Color Grading:\n`;

        const O_background_details_for_clean_mode = { selectedSolidBgColor, selectedBackgroundStyleId }; // O var
        // Use the new comprehensive background function
        cleanPrompt += getThePerfectBackgroundMyDude(userPrompt, selectedBackgroundType, O_background_details_for_clean_mode, false);
        
        // Override with a very simple fallback if userPrompt is empty and no specific type was chosen by getThePerfectBackgroundMyDude
        if ((!userPrompt || userPrompt.trim().length < 5) && !selectedBackgroundType) {
             // This specific fallback ensures a simple, neutral background when there's truly no input.
             // getThePerfectBackgroundMyDude might have already applied a similar fallback, but this is a final safety net.
             cleanPrompt = `Create a cinematic YouTube thumbnail image at 1280x720 resolution for a background ONLY.\n\nBackground Style & Color Grading:\n- Use a subtle, neutral background with a dark gray to black color gradient and a soft vignette texture. Ensure it is suitable as a versatile background layer.\n`;
        }

        // Apply LUT Color Grading Description if selected
        if (selectedColorGrade && colorMoodPresets[selectedColorGrade]) { // Use the new colorMoodPresets
            const lutDescription = colorMoodPresets[selectedColorGrade];
            cleanPrompt += `\nColor Grading:\n- Apply a ${lutDescription} color grade effect across the entire image. This grading should enhance the mood of the background style chosen above.\n`;
        }
        
        cleanPrompt += `\nStrict Adherence Required for Background-Only Mode:\n`;
        cleanPrompt += `- **CRITICALLY IMPORTANT: Do NOT include any human figures, faces, characters, animals, or distinct animate objects of any kind.**\n`;
        cleanPrompt += `- **ABSOLUTELY NO text, headlines, words, letters, numbers, or any form of typography shall appear anywhere on this image.**\n`;
        cleanPrompt += `- **STRICTLY FORBIDDEN: Do NOT include any illustrative icons, emojis, logos, or any distinct graphical shapes separate from the inherent background texture or pattern itself.**\n`;
        cleanPrompt += `- The image generated must be purely a background, suitable for later manual editing or use as a versatile backdrop for various content. It should be visually appealing on its own but clearly intended as a background layer.\n`;

        cleanPrompt += `\nOutput Format & Quality (Background-Only):\n`;
        cleanPrompt += `- Resolution: 1280x720 (16:9 aspect ratio).\n`;
        cleanPrompt += `- Style: Cinematic, high sharpness, and vibrant saturation (unless a muted color grade is selected).\n`;
        cleanPrompt += `- Ensure the background is well-composed and visually balanced across the entire canvas.\n`;
        cleanPrompt += `- Overall a clean, professional, and highly versatile background image.\n`;
        
        if (fitFullCanvas) {
            cleanPrompt += `\nFit Full Canvas (ON):\n`;
            cleanPrompt += `Generate the background to fill the entire 1280x720 frame. It must extend to all edges without any visible borders, padding, or letterboxing. The composition should feel complete and full.\n`;
        } else {
            cleanPrompt += `\nFit Full Canvas (OFF):\n`;
            cleanPrompt += `The background should be well-composed. Some subtle background padding or slight, tasteful letterboxing (e.g. thin dark bars top/bottom if it fits a cinematic style) is acceptable if it enhances the overall composition of the background itself, but avoid excessive empty or plain space. Focus on a strong visual center or flow.
`;
        }
        return cleanPrompt;
    }

    // LUT Color Grading Descriptive Phrases Mapping
    const lutPromptMap = {
        'cinematic-warm': 'cinematic orange-teal LUT with warm shadows and cool highlights',
        'cold-drama': 'cold blue-gray cinematic filter with desaturated highlights',
        'viral-energy': 'high-contrast vivid tone with saturated reds and yellows',
        'soft-calm': 'pastel color grade with muted tones and soft ambient lighting',
        'gaming-glow': 'neon lighting effects with purple and electric blue tones and digital bloom',
        'sunset-mood': 'soft peach and orange gradient overlay with warm lowlight ambience',
        'retro-pop': '80s pop art grading with magenta, yellow, and cyan tones'
    };

    // Define position descriptions (only relevant if textOverlay is ON)
    const positionDescription = {
        "Top Left": "Position the title text block in the upper-left corner.",
        "Top Center": "Center the title text block horizontally at the top.",
        "Top Right": "Position the title text block in the upper-right corner.",
        "Center": "Place the title text block directly in the center of the image.",
        "Bottom Left": "Position the title text block in the bottom-left corner.",
        "Bottom Center": "Center the title text block horizontally at the bottom.",
        "Bottom Right": "Position the title text block in the bottom-right corner.",
    };

    // Define the edge safe zone reinforcement text (only relevant if textOverlay is ON)
    const edgeSafeZoneInstruction = "Ensure the entire text overlay, including shadow and glow, is fully visible and **at least 100px away from all edges (top, bottom, left, right)**. If necessary to fit, reduce font size or wrap text creatively to prevent cropping. Never allow text or effects to touch or cross the image border.";

    // Start with the base instruction
    let prompt = `Create a cinematic YouTube thumbnail image at 1280x720 resolution.\n\n`;

    // Subject Section - Conditional based on includePerson
    if (includePerson) {
        prompt += `Subject:\n`;
        
        // Enhanced gender description with more precise guidance for the AI
        let genderDesc = "a human figure with neutral gender presentation";
        let genderAttributes = "";
        
        if (selectedGender === "Male") {
            genderDesc = "a male human figure";
            genderAttributes = " with masculine facial features, body type, and styling";
        } else if (selectedGender === "Female") {
            genderDesc = "a female human figure";
            genderAttributes = " with feminine facial features, body type, and styling";
        } else if (selectedGender === "Non-binary") {
            genderDesc = "a non-binary human figure";
            genderAttributes = " with androgynous or gender-neutral facial features and styling";
        } else { // Auto - let AI decide based on context
            genderDesc = "a human figure";
            genderAttributes = " with gender presentation that fits naturally with the thumbnail's topic and context";
        }

        let expressionInstruction = "";
        if (selectedExpression && selectedExpression !== 'Default') {
            expressionInstruction = ` with an expressive ${selectedExpression.toLowerCase()} facial emotion`;
        }

        if (faceDescription && faceDescription.startsWith('[')) { // Placeholder for uploaded image
             prompt += `- Faithfully replicate the facial features, hairstyle, skin tone, and overall likeness of ${genderDesc}${expressionInstruction} **exactly as shown in the uploaded face image**. Make this person the primary focus. Pose: facing camera, pointing, reacting, or holding a relevant object. Ensure the generated face clearly matches the reference.\n`;
        } else if (faceDescription) { // URL provided
            prompt += `- Faithfully replicate the facial features, hairstyle, skin tone, and overall likeness of ${genderDesc}${expressionInstruction} from the reference image at ${faceDescription}. Make this person the primary focus. Pose: facing camera, pointing, reacting, or holding a relevant object. Ensure the generated face clearly matches the reference.\n`;
        } else { // No custom face, AI generates
            prompt += `- Include ${genderDesc}${genderAttributes}${expressionInstruction}. Make this person the primary focus. Pose: facing camera, pointing, reacting, or holding a relevant object related to "${userPrompt}".\n`;
        }
        prompt += `\n`;
    } else {
        // If no person, describe the scene based on user prompt but explicitly say no human figures.
        prompt += `Subject: A scene based on the prompt: \"${userPrompt}\". **Do NOT include any human figures, faces, or characters.**\n\n`;
    }

    // Text Overlay Section - Conditional based on textOverlay
    if (textOverlay) {
        prompt += `Text Overlay:\n`;
        if (overlayText && overlayText.trim() !== '') {
            // Replace placeholders in overlayText with generic terms if not filled by user
            const finalOverlayText = overlayText.replace(/\[([A-Z0-9_\s]+)\]/g, (match, p1) => `(${p1.toLowerCase()})`);
            prompt += `- Add a bold, uppercase title: "${finalOverlayText.replace(/\n/g, '\\n')}"\n`;
        } else {
            prompt += `- Add a bold, uppercase title related to the video topic: "${userPrompt}"\n`;
        }

        let basePlacement = positionDescription[overlayPosition] || positionDescription["Top Right"];
        let finalPlacementInstruction = `${basePlacement} ${edgeSafeZoneInstruction}`;

        // Add text size instruction based on selectedTextSize
        let textSizeInstruction = "";
        // REVISED TEXT SIZE LOGIC
        if (selectedTextSize === "Small") {
            textSizeInstruction = `\\n- Text Size: Use small, subtle title text. It should be clearly legible but occupy minimal space and **never distract** from the main subject or background. Always keep the text well within the 100px safe zone from all edges. Prioritize minimalism and aesthetic balance.`;
        } else if (selectedTextSize === "Medium") {
            textSizeInstruction = `\\n- Text Size: Use moderately sized title text. It should be prominent and easily readable but well-balanced with other visual elements, and never dominate the entire thumbnail. Ensure the text is comfortably within the 100px safe zone from all edges.`;
        } else if (selectedTextSize === "Large") {
            textSizeInstruction = `\\n- Text Size: Use large, attention-grabbing title text. While it should be a major focal point, it MUST NOT touch or crowd the edges. Ensure a clear margin of at least 100px from all edges. If the text is too long, reduce font size or wrap it to fit comfortably and legibly within this safe zone. Prioritize readability and overall visual balance over maximum possible size.`;
        } else { // Default or other unspecified sizes (should ideally be Medium or a defined default)
            textSizeInstruction = `\\n- Text Size: Use a default, balanced text size (similar to Medium). It should be clearly legible and fit well within a 100px safe zone from all edges.`;
        }

        prompt += `- Font: Use font family '${selectedFontFamily}'. Ensure it is bold, highly legible, and uppercase.${textSizeInstruction}\n`;
        prompt += `- Colors: Use primary color '${primaryTextColor}' and secondary color '${secondaryTextColor}'. Create high contrast (e.g., vibrant yellow text with a deep black outline/glow, or crisp white text on a dark, shaped background element). Ensure maximum readability against the background.\n`;
        prompt += `- Text Effects: Ensure text is CRISPY and sharp. Apply a STRONG, vibrant GLOW effect around the text (e.g., a neon glow or a soft bloom, color-coordinated with text/background), and a noticeable, clean DROP SHADOW to make it pop dramatically from the background. The glow and shadow should enhance readability and visual appeal, not clutter it.\n`;
        prompt += `- Placement & Safe Zone: ${finalPlacementInstruction}\n`;
        prompt += `\n`;
    } else {
        prompt += `**Ensure NO text, headlines, or words appear anywhere on the image.**\n\n`;
    }

    // Visual Focus & Icons - Conditional based on includeIcons
    prompt += `Visual Focus & Composition:\n`;
    prompt += `- Keep layout simple and high-impact with a strong center or rule-of-thirds composition.\n`;
    if (includeIcons) {
        prompt += `\n--- Enhanced 3D Icon Instructions ---\n`;
        prompt += `- Render 1-3 icons in a modern, 3D style with realistic depth and dimension.\n`;
        prompt += `- Apply subtle isometric perspective (15-30 degree angle) for natural depth to all icons.\n`;
        prompt += `- Icons should have realistic lighting and shadows that match the thumbnail's main light source. Include subtle ambient occlusion in corners and crevices for all icons.\n`;
        prompt += `- Use metallic or glossy materials for icons where appropriate (e.g., tech icons, gaming elements, trophies).\n`;

        // Determine if this is a gaming prompt to decide icon style and specific enhancements
        const isGamePrompt = hasGamingBrand(detectedBrandLogos);

        if (detectedBrandLogos && detectedBrandLogos.length > 0) {
            // Build brand instructions
            const brandNames = detectedBrandLogos.map(b => b.name).join(', '); // Use .name property
            prompt += `- Integrate the **official** ${brandNames} logo${detectedBrandLogos.length > 1 ? 's' : ''} into the composition. Maintain original aspect ratio and brand colors. Each logo should be noticeable yet subtle, occupying no more than 10% of the thumbnail height and placed at least 100px from all edges. Avoid distorting or recoloring the logo assets.\n`;

            if (detectedBrandLogos.length === 2) {
                prompt += `  - Since two brands are mentioned, position the logos on opposite sides of the frame (e.g., left vs right) or use a split-screen layout to emphasize comparison.\n`;
            }
        }

        if (isGamePrompt) {
            prompt += `- For these gaming-related icons: Include authentic in-game GUI or HUD elements, weapon silhouettes, or ability icons from the specific game mentioned in "${userPrompt}". Icons MUST match the real game's style and interface but rendered in a 3D style. Examples: 3D crosshair, 3D minimap element, 3D health bar segment, 3D grenade, 3D gun, 3D shield, 3D ability icon. Include particle effects and energy auras relevant to the game's aesthetic.\n`;
        } else if ((userPrompt || '').toLowerCase().includes('tech')) {
            prompt += `- For these tech-related icons: Add subtle circuit patterns and glowing elements to their 3D design.\n`;
        } else {
            prompt += `- For general icons: Include 1–3 modern, relevant 3D icons that fit the topic of "${userPrompt}". Icons should be clean and visually appealing.\n`;
        }
        
        prompt += `\n- Icon Effects & Details (Apply to all 3D icons):\n`;
        prompt += `  - Add subtle depth of field blur to icons if they are in the background or to create focus.\n`;
        prompt += `  - Consider particle effects or energy trails for dynamic icons if relevant to "${userPrompt}".\n`;
        prompt += `  - Apply realistic reflections and highlights based on the scene lighting.\n`;
        prompt += `  - Use volumetric lighting to create a sense of atmosphere around icons if it fits the scene.\n`;
        prompt += `  - Add subtle motion blur for action-oriented icons if applicable.\n`;

        prompt += `\n- Icon Integration (Apply to all 3D icons):\n`;
        prompt += `  - Position icons in 3D space with proper perspective and scale relative to other elements.\n`;
        prompt += `  - Create visual hierarchy through size and depth placement of icons.\n`;
        prompt += `  - Ensure icons cast realistic shadows onto the background or other surfaces.\n`;
        prompt += `  - Use depth-based color adjustments (e.g., slightly darker in shadows, brighter in highlights).\n`;
        prompt += `  - Maintain consistent lighting direction across all icons and with the main scene.\n`;

        prompt += `\n- Quality & Consistency (Apply to all 3D icons):\n`;
        prompt += `  - Maintain a consistent 3D style and quality across all icons.\n`;
        prompt += `  - Ensure icons remain recognizable and readable even at smaller sizes.\n`;
        prompt += `  - Effects should be subtle and professional, enhancing rather than cluttering.\n`;
        prompt += `  - Icons should complement the main subject and overall thumbnail theme.\n`;
        prompt += `  - All icons must be fully visible and kept at least 100px away from every edge.\n`;
        prompt += `  - **Do NOT use generic 2D emojis or flat shapes. All icons must be rendered with 3D depth and dimension.**\n`;
    } else {
        prompt += `- Do not include any illustrative icons, emojis, or generic shapes unless they are naturally embedded in the subject (e.g., a product logo on a device).\n`;
    }
    prompt += `\n`;

    // Background Section
    prompt += `\nBackground Style & Color Grading:\n`;

    // Use the new comprehensive background generation function
    const I_details_for_background_blah_blah = { selectedSolidBgColor, selectedBackgroundStyleId }; // I var
    prompt += getThePerfectBackgroundMyDude(userPrompt, selectedBackgroundType, I_details_for_background_blah_blah, includePerson);

    // Common instructions for all background types (can be refined based on type)
    prompt += `Incorporate creative background elements where appropriate for the selected style or user prompt: abstract shapes, subtle patterns, motion blur, light streaks, or bokeh effects, IF they enhance the mapped background and don't contradict it.\n`;
    prompt += `Rotate and flip the color/gradient direction or lighting angle for each new thumbnail to avoid visual repetition, unless a specific template or mapped style dictates a fixed direction (e.g., a specific sunset direction).\n`;
    prompt += `Ensure the background ALWAYS provides strong contrast with the subject (if any) and overlay text (if any) for maximum readability and click appeal. This is a critical rule.\n`;

    // Inject LUT Color Grading Description
    if (selectedColorGrade && colorMoodPresets[selectedColorGrade]) { // Use the new colorMoodPresets
        const lutDescription = colorMoodPresets[selectedColorGrade];
        prompt += `\nColor Grading:\n- Apply a ${lutDescription} color grade effect across the entire image, including subject and background. This grading should unify the image and enhance the desired mood (e.g., 'viral-energy' for exciting content, 'soft-calm' for relaxing topics). Ensure the color grade does not wash out important details or reduce contrast too much.\n`;
    } else {
        // Optional: Add default or no grading instruction if needed
        prompt += `\nColor Grading:\n- Apply a standard, natural color balance that enhances sharpness and vibrancy without overtly stylizing the colors, unless a specific background style implies its own color grading (like a sepia tone for a historical theme if chosen by the background generator).\n`;
    }

    // Output Format & Universal Quality Rules
    prompt += `Output Format & Quality:\n`;
    prompt += `- Resolution: 1280x720 (16:9 aspect ratio).\n`;
    prompt += `- Style: Cinematic, high sharpness, and vibrant saturation, suitable for a YouTube thumbnail.\n`;
    prompt += `- Ensure all elements are clearly visible on both mobile and desktop screens.\n`;
    prompt += `- Overall a clean, professional, and click-worthy thumbnail.\n`;
    prompt += `- IMPORTANT PREVIEW CROPPING & SAFE ZONE: The preview container in the web app might crop the top and bottom edges. To prevent text or important elements from being cut off in the preview, ensure a minimum safe zone of at least 10% of the image height (which is 72px, so aim for **at least 100px**) from the top AND bottom edges. All text, faces, and key icons MUST be fully visible within this central safe area. Do NOT place any critical text or elements near the very top or bottom edge; keep them centered or use rule-of-thirds. The final exported image will show the full 1280x720 area, but the preview might crop more—design accordingly for a good preview experience.\n`;

    if (fitFullCanvas) {
        prompt += `\nFit Full Canvas (ON):\n`;
        prompt += `Generate the thumbnail to fill the entire 1280x720 frame. The background should extend to all edges without any visible borders, padding, or letterboxing. Ensure all critical elements (like faces or text if present) are within a safe zone of at least 100px from all edges (top, bottom, left, right) to accommodate potential preview cropping and ensure full visibility on YouTube.\n`;
    } else {
        prompt += `\nFit Full Canvas (OFF):\n`;
        prompt += `The main subject and action should be clearly visible and well-composed. Some background padding or slight letterboxing is acceptable if it enhances the composition, but avoid excessive empty space.\n`;
    }

    // If both are off, add a specific instruction for background-only mode.
    if (!includePerson && !textOverlay) {
        prompt += `\n**IMPORTANT: BACKGROUND-ONLY MODE (Fallback within main prompt - should ideally use cleanPrompt mode)**\n`;
        prompt += `- Generate a high-quality, visually appealing background scene based on the prompt: "${userPrompt}".\n`;
        prompt += `- This image is intended as a background layer, so ensure it is compositionally balanced to allow for later manual placement of text or subjects in an external editor.\n`;
        prompt += `- NO human figures, NO characters, and NO text overlays should be present in the generated image. This is background ONLY.\n`;
    }

    return prompt;
}

// Representation of thumbnail-logic.yaml background_logic.smart_mapping
const smartBackgroundMappings = {
    gaming: {
        fortnite: "a dynamic Fortnite-style build battle background with recognizable in-game elements like wooden structures, ramps, and a vibrant sky, possibly with storm effects or iconic map locations in the distance. Add an abstract sunburst effect in the background for extra energy and visual impact.",
        warzone: "a gritty, realistic military battlefield at dusk or dawn, featuring urban ruins, tactical gear elements, or distant combat effects like smoke and tracers. Cinematic and tense. Incorporate an abstract sunburst background to enhance drama and focus.",
        csgo: "an urban combat scene reminiscent of Counter-Strike maps, with graffiti, damaged buildings, and an overall tense, competitive atmosphere. Balanced for readability. Overlay an abstract sunburst background for added intensity.",
        minecraft: "a blocky, pixel-art landscape or textures in Minecraft style. Bright and engaging, with characteristic Minecraft elements. Include a subtle abstract sunburst background to boost vibrancy.",
        'battle royale': "an atmospheric background evoking the specific battle royale game mentioned. Could be map overviews, weapon silhouettes, or action effects like smoke or explosions, with a gritty, competitive feel. Add an abstract sunburst background for dynamic energy.",
        'mobile game': "a background incorporating mobile UI elements, touch control overlays, or a stylized mobile device frame. Bright and engaging, reflecting the game's art style. Integrate an abstract sunburst background for extra excitement."
    },
    tech: {
        'apple review': "a sleek, modern, minimalist tech setup background. Could feature soft lighting, brushed aluminum textures, or abstract representations of Apple's design language. Clean and premium.",
        'app comparison': "a split background visually separating two distinct UI styles or color themes, perhaps with a subtle dividing line or contrasting textures. Modern and clear.",
        'software': "an abstract digital background with faint code snippets, glowing data streams, or a futuristic interface aesthetic. Dark mode preferred, with pops of color.",
        'gadget': "a clean, modern aesthetic with circuit patterns, abstract digital gradients, or a sleek futuristic look. May include close-ups of device textures if relevant to the userPrompt.",
        'tech review': "a sophisticated background that blends abstract technology motifs (like circuits or data flows) with elements specific to the reviewed product category (e.g., for a phone review, perhaps a stylized representation of a smartphone component).",
    },
    reaction: {
        cringe: "a vibrant, colorful radial comic book style burst effect (like 'POW!' or 'CRINGE!'), with dynamic lines and possibly halftone patterns. Energetic and humorous.",
        scary: "a dark, atmospheric background with deep reds, blues, or purples. Cinematic lighting with strong shadows, possibly with fog, mist, or unsettling textures. Suspenseful.",
        'reacting to': "a dynamic and expressive background, perhaps with pop-art halftone patterns, abstract emotional color splashes, or subtle visual cues from what is being reacted to (if implied by userPrompt).",
    },
    unboxing: {
        product: "a dramatic background with a glowing box or product silhouette at the center. Use volumetric lighting, light rays, and a sense of anticipation. Premium and exciting.",
        'mystery box': "visually exciting elements like abstract packaging textures, surprise effects (e.g., light rays, particles), product silhouettes shrouded in mystery, or a sense of anticipation and curiosity."
    },
    vlogs: {
        default: "scenic and aesthetically pleasing lifestyle, travel, or daily environment scenes matching the specific topic of userPrompt. If travel, show iconic landmarks blurred or stylized. If daily life, a cozy or dynamic setting reflecting the vlog's theme."
    },
    tutorials: {
        default: "a clean and professional background, possibly featuring abstract representations of whiteboards, code editors (if relevant), or relevant workspace imagery. Avoid clutter and maintain focus on clarity."
    }
};

// Representation of thumbnail-logic.yaml color_moods.presets
// Aligned with existing lutPromptMap and YAML, preferring more descriptive for AI
const colorMoodPresets = {
    'cinematic-warm': 'cinematic orange-teal LUT with warm shadows and cool highlights, enhancing depth and mood',
    'cold-drama': 'cold blue-gray cinematic filter with desaturated highlights and crushed blacks, creating a dramatic and moody atmosphere',
    'gaming-glow': 'neon lighting effects with vibrant purple, electric blue, and magenta tones, digital bloom, and high contrast, perfect for gaming content',
    'viral-energy': 'high-contrast vivid color grading with saturated reds, yellows, and cyans, creating an energetic and eye-catching look',
    'soft-calm': 'pastel color grade with muted tones, soft ambient lighting, and a gentle haze, for a relaxing and aesthetic vibe',
    'sunset-mood': 'soft peach and orange gradient overlay with warm lowlight ambience, evoking a sunset atmosphere',
    'retro-pop': '80s pop art grading with bold magenta, yellow, and cyan tones, possibly with halftone effects or geometric patterns'
};

// Per custom instructions, this function is long and has a unique style.
function getThePerfectBackgroundMyDude(userPromptString, selectedUserBackgroundType, someDetailsForBackground, includePerson) {
    let l_promptForBg = ""; // single letter var - prompt for background
    const O_userPromptLower = (userPromptString || "").toLowerCase().trim(); // another single letter var - user prompt lowercased

    // Keywords for new contextual backgrounds
    const cryptoKeywords = ['crypto', 'bitcoin', 'ethereum', 'blockchain', 'nft', 'decentralized'];
    const financeKeywords = ['finance', 'financial', 'invest', 'investment', 'stocks', 'market', 'trading', 'forex'];
    const moneyKeywords = ['money', 'cash', 'currency', 'rich', 'wealth', 'dollars', 'euros', 'yen', 'save money', 'making money'];
    const bankingKeywords = ['bank', 'banking', 'credit card', 'loan', 'mortgage', 'savings account'];
    const businessKeywords = ['business', 'corporate', 'startup', 'entrepreneur', 'company', 'enterprise', 'marketing', 'sales'];


    let I_isSpecificTopic = false; // another single letter var - flag for specific topic

    if (cryptoKeywords.some(keyword => O_userPromptLower.includes(keyword))) {
        l_promptForBg = "- Use a futuristic digital background with elements like glowing circuit patterns, abstract representations of blockchain technology, or large, stylized Bitcoin or Ethereum coins. The style should be modern, sleek, and tech-focused, often with dark tones and vibrant neon accents (blues, purples, greens).\n";
        I_isSpecificTopic = true;
    } else if (financeKeywords.some(keyword => O_userPromptLower.includes(keyword))) {
        l_promptForBg = "- Use a professional background incorporating financial charts (line graphs, bar charts, candlestick patterns), stock market tickers, or abstract representations of data and growth. Colors should be clean and trustworthy, often blues, greens, or a sophisticated dark theme with metallic accents.\n";
        I_isSpecificTopic = true;
    } else if (moneyKeywords.some(keyword => O_userPromptLower.includes(keyword))) {
        l_promptForBg = "- Use a background visually representing wealth or currency, such as artistic arrangements of physical money (stacks of cash, coins), digital currency symbols, or abstract gold/metallic textures. The vibe can range from luxurious to practical depending on the prompt's nuance.\n";
        I_isSpecificTopic = true;
    } else if (bankingKeywords.some(keyword => O_userPromptLower.includes(keyword))) {
        l_promptForBg = "- Use a background that evokes security and trust, like a modern bank vault (stylized, not too literal), abstract credit card designs, digital banking interfaces, or clean architectural lines of a bank building. Colors are often blues, grays, and whites, conveying stability.\n";
        I_isSpecificTopic = true;
    } else if (businessKeywords.some(keyword => O_userPromptLower.includes(keyword))) {
        l_promptForBg = "- Use a background depicting a modern corporate environment, such as an abstract office setting, cityscapes with skyscrapers, network diagrams, or sleek presentation graphics. Colors are typically professional: blues, grays, whites, with occasional bold accent colors for emphasis.\n";
        I_isSpecificTopic = true;
    }


    if (I_isSpecificTopic && includePerson) {
        l_promptForBg += "- Apply a subtle depth-of-field effect to this background, making it slightly blurry to ensure the main subject (person) and any text overlay remain sharp and visually prominent. The background context should still be recognizable.\n";
    } else if (includePerson && !I_isSpecificTopic) {
        // Apply blur for person even if not a specific finance/biz topic, if a general background is chosen by later logic
        l_promptForBg += "- If a general background is chosen, and a person is included, apply a subtle depth-of-field effect to the background, making it slightly blurry to ensure the main subject (person) and any text overlay remain sharp and visually prominent.\n";
    }


    if (I_isSpecificTopic) {
        // If a specific topic background was already set, we might return or add more generic instructions
        // For now, let's assume the specific topic prompt is sufficient.
        // We can add more shared instructions later if needed.
    } else if (selectedUserBackgroundType === 'solid' && someDetailsForBackground.selectedSolidBgColor) {
        l_promptForBg = `- Use a solid color background with the hex code: ${someDetailsForBackground.selectedSolidBgColor}. Add a very subtle radial gradient or vignette to give it some depth, but it should primarily appear as a solid color.\n`;
         if (includePerson) {
            l_promptForBg += "- Apply a subtle depth-of-field effect to this background, making it slightly blurry to ensure the main subject (person) and any text overlay remain sharp and visually prominent.\n";
        }
    } else if (selectedUserBackgroundType === 'template' && someDetailsForBackground.selectedBackgroundStyleId) {
        const l_styleId = someDetailsForBackground.selectedBackgroundStyleId;
        let l_templateDesc = `a pre-designed background template identified by '${l_styleId}'.`;

        // Attempt to find a more descriptive prompt from smartBackgroundMappings
        for (const category in smartBackgroundMappings) {
            if (smartBackgroundMappings[category][l_styleId]) {
                l_templateDesc = smartBackgroundMappings[category][l_styleId];
                    break;
                }
            }
        l_promptForBg = `- Use the background style: ${l_templateDesc}\n`;
        if (includePerson) {
            l_promptForBg += "- Apply a subtle depth-of-field effect to this background, making it slightly blurry to ensure the main subject (person) and any text overlay remain sharp and visually prominent.\n";
        }

    } else { // Fallback to general descriptive background based on user prompt
        if (O_userPromptLower.length > 0) {
            l_promptForBg = `- Generate a background that is visually relevant to the theme: "${userPromptString}". It should be cinematic and enhance the main topic without being distracting.\n`;
        } else {
            l_promptForBg = `- Use a neutral, abstract background with a dark gradient (e.g., dark gray to black) and subtle textures. It should be versatile and professional.\n`;
        }
        if (includePerson) {
             // This condition was already handled above if !I_isSpecificTopic, but being explicit here for fallback
            l_promptForBg += "- Apply a subtle depth-of-field effect to this background, making it slightly blurry to ensure the main subject (person) and any text overlay remain sharp and visually prominent.\n";
        }
    }
    
    // Ensure a base instruction is always present if l_promptForBg somehow remained empty
    if (l_promptForBg.trim() === "") {
        l_promptForBg = `- Use a versatile, neutral, abstract background with a dark gradient and subtle textures. It should be suitable for various topics and maintain a professional look.\n`;
        if (includePerson) {
            l_promptForBg += "- Apply a subtle depth-of-field effect to this background, making it slightly blurry to ensure the main subject (person) and any text overlay remain sharp and visually prominent.\n";
        }
    }

    l_promptForBg += `Always ensure the background provides strong visual contrast with any foreground elements (like a person or text) if they are to be added. The background itself should be high quality, with good lighting and composition, even if it's meant to be subtle or blurred.\n`;
    return l_promptForBg;
}
