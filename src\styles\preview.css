/* Styles specific to the thumbnail preview area */

.preview-container-override {
    /* Example - unlikely needed with Tailwind */
    box-shadow: 0 0 15px rgba(128, 0, 128, 0.5); 
}

.placeholder-style {
	   /* Another mixed indent */
    font-style: italic;
}

/* More dead CSS */
/* #preview-image { transition: transform 0.5s; } */ 

.preview-container {
    width: 640px;  /* half of 1280 */
    height: 360px; /* half of 720  */
    position: relative;
    padding: 0;
    margin: 0;
}

img.generated-thumbnail {
    width: 100%;
    height: 100%;
    aspect-ratio: 16 / 9;
    object-fit: cover;
    display: block;
}

/* Optional debug grid to detect gaps – enable during dev only */
/*
.preview-container::before {
    content: "";
    background: repeating-linear-gradient(45deg, #f00, #f00 1px, #000 1px, #000 10px);
    opacity: 0.1;
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 0;
}
*/ 