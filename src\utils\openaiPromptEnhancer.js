// New utility to call OpenAI Chat Completion for smart prompt enhancement
import { OPENAI_API_KEY } from '/config.js';

// Named export so callers can import { getSmartTitle }
export const getSmartTitle = async (rawPrompt) => {
    if (!rawPrompt) return '';

    // Safety: trim and limit to 200 characters to keep request concise
    const userIdea = rawPrompt.trim().slice(0, 200);

    const payload = {
        model: 'gpt-3.5-turbo-1106', // cost-effective model for short copywriting
        messages: [
            {
                role: 'system',
                content: [
                    'You are an expert YouTube copywriter. ',
                    'Rewrite the user\'s rough video idea into a SINGLE professional, catchy, YouTube-ready title. ',
                    'Key rules:\n',
                    '• Keep it under 90 characters.\n',
                    '• Use Title Case capitalization.\n',
                    '• Do NOT wrap the title in quotes or markdown.\n',
                    '• The title must stay faithful to the user\'s original topic and intent.\n',
                    '• Return ONLY the title text.'
                ].join('')
            },
            {
                role: 'user',
                content: userIdea
            }
        ],
        temperature: 0.7,
        max_tokens: 20,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0
    };

    try {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${OPENAI_API_KEY}`
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`OpenAI API error: ${response.status} ${errorText}`);
        }

        const data = await response.json();
        const rawTitle = data.choices?.[0]?.message?.content || '';

        // Clean up any stray quotes/newlines
        return rawTitle.trim().replace(/^"|"$/g, '').replace(/^'|'$/g, '');
    } catch (err) {
        console.error('Failed to generate smart title:', err);
        throw err; // Propagate so caller can fallback
    }
}; 