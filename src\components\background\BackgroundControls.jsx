/**
 * BackgroundControls.jsx
 * Component for background category selection & modal trigger
 */

// Since we're using the window.React approach as seen in other components
const React = window.React;
const { useState, useEffect } = React;

import { BACKGROUND_CATEGORIES } from '../../utils/backgroundConfig.js';
import { BackgroundTemplateModal } from './BackgroundTemplateModal.jsx';

export const BackgroundControls = ({
  selectedBackgroundType,
  selectedBackgroundStyleId,
  handleBackgroundStyleSelect,
  selectedSolidBgColor,
  handleSolidBgColorChange
}) => {
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showSolidColors, setShowSolidColors] = useState(false);
  
  // Find the solid color category for direct display
  const solidColorCategory = BACKGROUND_CATEGORIES.find(cat => cat.id === 'solid');
  
  // Open modal with selected category or toggle solid colors display
  const handleCategoryClick = (category) => {
    if (category.id === 'solid') {
      // For solid colors, toggle the inline display instead of opening modal
      setShowSolidColors(!showSolidColors);
      setSelectedCategory(category);
    } else {
      // For other categories, open the modal
      setSelectedCategory(category);
      setIsModalOpen(true);
      // Hide solid colors if they were showing
      setShowSolidColors(false);
    }
  };
  
  // Close modal
  const handleCloseModal = () => {
    setIsModalOpen(false);
  };
  
  // Handle style selection from modal
  const handleStyleSelect = (styleId) => {
    // For solid colors with isCustom property, we handle differently
    const { category, style } = findCategoryAndStyle(styleId);
    
    if (category && category.id === 'solid' && style && style.isCustom) {
      // For custom solid color option, keep the modal open and focus on color picker
      handleBackgroundStyleSelect(styleId, 'solid');
    } else if (category && style) {
      // For regular styles
      const type = category.id === 'solid' ? 'solid' : 'template';
      
      // If it's a solid color with predefined color, pass that color
      if (type === 'solid' && style.color) {
        handleBackgroundStyleSelect(styleId, type, style.color);
      } else {
        handleBackgroundStyleSelect(styleId, type);
      }
      
      if (isModalOpen) {
        handleCloseModal();
      }
    }
  };
  
  // Find category and style by styleId
  const findCategoryAndStyle = (styleId) => {
    for (const category of BACKGROUND_CATEGORIES) {
      const style = category.styles.find(s => s.id === styleId);
      if (style) {
        return { category, style };
      }
    }
    return { category: null, style: null };
  };

  // Get the current selected style and category for UI highlighting
  const getCurrentSelection = () => {
    if (!selectedBackgroundStyleId) return { category: null, style: null };
    return findCategoryAndStyle(selectedBackgroundStyleId);
  };

  const { category: currentCategory, style: currentStyle } = getCurrentSelection();
  
  // Handle reset to default
  const handleResetToDefault = () => {
    handleBackgroundStyleSelect(null, null);
    setShowSolidColors(false);
  };
  
  // Render solid color options in a grid
  const renderSolidColorOptions = () => {
    if (!solidColorCategory || !showSolidColors) return null;
    
    return React.createElement('div', { 
      id: 'bgCtrl-solidColorPanel',
      className: 'mt-4 bg-gray-800/50 p-4 rounded-lg bgCtrl-solidColorContainer'
    },
      React.createElement('div', { 
        id: 'bgCtrl-solidColorHeader',
        className: 'text-sm font-medium text-gray-300 mb-3 flex items-center justify-between bgCtrl-solidColorHeader' 
      },
        React.createElement('span', {}, 'Choose a solid color:'),
        React.createElement('button', {
          id: 'bgCtrl-hideColorsBtn',
          className: 'text-xs text-purple-400 hover:text-purple-300 bgCtrl-hideColors',
          onClick: () => setShowSolidColors(false)
        }, 'Hide colors')
      ),
      React.createElement('div', { 
        id: 'bgCtrl-colorGrid',
        role: 'grid',
        'aria-label': 'Solid color options',
        className: 'grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-3 auto-rows-fr bgCtrl-colorGridContainer' 
      },
        solidColorCategory.styles.map(style => {
          const isSelected = selectedBackgroundStyleId === style.id;
          
          // For custom color option
          if (style.isCustom) {
            return React.createElement('div', {
              key: style.id,
              id: `bgCtrl-customColor-${style.id}`,
              className: `color-picker-card p-3 bg-gray-700 border-2 ${isSelected ? 'border-purple-500' : 'border-gray-600'} rounded-lg flex flex-col gap-2 bgCtrl-customColorCard`
            },
              React.createElement('label', {
                htmlFor: 'customColorPicker',
                className: 'text-xs font-medium text-gray-300 mb-1 bgCtrl-customColorLabel'
              }, 'Custom:'),
              React.createElement('input', {
                id: 'customColorPicker',
                type: 'color',
                value: selectedSolidBgColor || '#000000',
                onChange: (e) => handleSolidBgColorChange(e.target.value),
                className: 'w-full h-10 cursor-pointer border rounded-md overflow-hidden bgCtrl-colorPickerInput',
                style: { backgroundColor: 'transparent' }
              }),
              React.createElement('button', {
                id: `bgCtrl-selectCustomBtn`,
                onClick: () => handleStyleSelect(style.id),
                className: `mt-1 px-2 py-1 text-xs ${isSelected ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'} rounded-md transition-colors bgCtrl-selectCustomBtn`,
              }, isSelected ? 'Selected' : 'Select')
            );
          }
          
          // Regular color swatch
          return React.createElement('button', {
            key: style.id,
            id: `bgCtrl-colorSwatch-${style.id}`,
            onClick: () => handleStyleSelect(style.id),
            className: `color-swatch flex flex-col rounded-lg overflow-hidden border-2 ${isSelected ? 'border-purple-500 shadow-lg' : 'border-gray-700 hover:border-purple-400'} h-24 transition-all focus:ring-2 focus:ring-purple-500 focus:outline-none bgCtrl-colorSwatch`,
            'aria-label': `Select ${style.name} color`,
            'aria-pressed': isSelected
          },
            // Color preview
            React.createElement('div', {
              className: 'h-12 w-full bgCtrl-colorPreview',
              style: { backgroundColor: style.color }
            }),
            // Color name and selected state
            React.createElement('div', {
              className: 'p-2 bg-gray-700 flex-1 flex flex-col justify-between bgCtrl-colorSwatchInfo'
            },
              React.createElement('span', { className: 'text-xs font-medium bgCtrl-colorName' }, style.name),
              isSelected && React.createElement('div', { className: 'mt-1 flex justify-end bgCtrl-selectedIndicator' },
                React.createElement('span', { 
                  className: 'iconify text-purple-500 text-sm bgCtrl-checkIcon', 
                  'data-icon': 'solar:check-circle-bold' 
                })
              )
            )
          );
        })
      )
    );
  };

  // Render the category grid and selected category's styles
  return (
    React.createElement('div', { 
      id: 'bgCtrl-container',
      className: 'background-controls w-full bgCtrl-container' 
    },
      // Reset button at top-right
      React.createElement('div', { 
        id: 'bgCtrl-resetBtnContainer',
        className: 'flex justify-end mb-3' 
      },
        React.createElement('button', {
          id: 'bgCtrl-resetBtn',
          onClick: handleResetToDefault,
          className: 'flex items-center gap-2 px-3 py-1.5 bg-transparent border border-gray-600 text-gray-400 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-gray-700/30 hover:text-gray-300 hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900',
          'aria-label': 'Reset to default background'
        }, 
          React.createElement('span', {
            className: 'iconify',
            'data-icon': 'solar:refresh-bold-duotone',
            style: { width: '16px', height: '16px', color: '#F9C97C' }
          }),
          'Reset to Default'
        )
      ),
      
      // Instruction text
      React.createElement('p', { 
        id: 'bgCtrl-instructions',
        className: 'text-sm text-gray-400 mb-3 bgCtrl-instructions' 
      }, 'Choose a background style below, or reset to default.'),
      
      // Category Grid - updated with visual block styling
      React.createElement('div', { 
        id: 'bgCtrl-categoryGrid',
        role: 'grid',
        'aria-label': 'Background categories',
        className: 'bgCtrl-categoryGridContainer grid grid-cols-3 gap-3',
        style: { width: 'calc(100% - 4px)', margin: '0 auto' }
      }, 
        BACKGROUND_CATEGORIES.map(category => {
          const isSelected = currentCategory && currentCategory.id === category.id;
          const isActive = isSelected || (category.id === 'solid' && showSolidColors);
          
          return React.createElement('div', {
            key: category.id,
            id: `bgCtrl-category-${category.id}`,
            role: 'gridcell',
            onClick: () => handleCategoryClick(category),
            className: `category-block flex flex-col items-center justify-center p-3 rounded-lg cursor-pointer
              ${isActive
                ? 'bg-purple-900/30 border-2 border-purple-500 shadow-lg' 
                : 'bg-gray-700/50 border border-gray-700 hover:border-purple-400 hover:bg-gray-700/70'}
              transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 h-28 bgCtrl-categoryBlock`,
            tabIndex: '0',
            'aria-label': `${category.name} backgrounds`,
            'aria-selected': isActive,
            onKeyDown: (e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleCategoryClick(category);
                e.preventDefault();
              }
            }
          },
            React.createElement('span', { 
              className: `iconify text-3xl mb-2 ${isActive ? 'text-purple-300' : 'text-gray-300'} bgCtrl-categoryIcon`,
              'data-icon': category.icon 
            }),
            React.createElement('span', { 
              className: `text-sm font-medium ${isActive ? 'text-purple-300' : 'text-gray-300'} bgCtrl-categoryName`
            }, category.name),
            React.createElement('span', { 
              className: `text-xs mt-1 ${isActive ? 'text-purple-300/80' : 'text-gray-400'} bgCtrl-categoryCount`
            }, category.styles.length + ' styles')
          );
        })
      ),
      
      // Solid Color Options (conditionally displayed inline)
      renderSolidColorOptions(),
      
      // Background template modal (for non-solid categories)
      isModalOpen && selectedCategory && React.createElement(BackgroundTemplateModal, {
        category: selectedCategory,
        onClose: handleCloseModal,
        onSelectStyle: handleStyleSelect,
        selectedStyleId: selectedBackgroundStyleId
      })
    )
  );
};

export default BackgroundControls; 