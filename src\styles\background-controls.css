/**
 * background-controls.css
 * Styles for background-related components
 * 
 * Custom naming conventions:
 * - CP-*: ControlPanel component elements (prefixed with cp-)
 * - bgCtrl-*: BackgroundControls component elements (prefixed with bgCtrl-)
 * - bgModal-*: BackgroundTemplateModal component elements (prefixed with bgModal-)
 */

/* ===========================
   Collapsible Sections
   =========================== */
.collapsible-section {
	position: relative;
}

.collapsible-section h3 {
	transition: color 0.2s ease;
}

.collapsible-section [aria-expanded="true"] h3 {
	color: rgb(216, 180, 254); /* purple-300 with slight brightness */
}

.collapsible-section [role="button"]:hover h3 {
	color: rgb(233, 213, 255); /* Lighter purple on hover */
}

.collapsible-content {
	will-change: max-height, opacity;
}

/* ===========================
   ControlPanel Background Section 
   =========================== */
.cp-backgroundSection {
	/* Container for the entire background section in ControlPanel */
}

.cp-backgroundHeader {
	/* Header for background section containing title and toggle */
}

.cp-backgroundTitle {
	/* "Background Styles" heading */
}

.cp-backgroundToggle {
	/* Toggle switch for expanding/collapsing background section */
}

.cp-backgroundCollapse {
	/* Container for the collapsible section */
}

/* ===========================
   Background Section Animations 
   =========================== */
.background-section-collapse {
	transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
	overflow: hidden;
}

.background-section-visible {
	max-height: screen;
	opacity: 1;
	margin-top: 0.75rem;
}

.background-section-hidden {
	max-height: 0;
	opacity: 0;
	margin-top: 0;
}

/* ===========================
   Background Style Selector
   =========================== */
.background-style-selector {
	/* Main container for the selector in App.jsx */
}

.background-style-categories {
	/* Container for category tiles grid */
}

.background-style-category-tile {
	position: relative;
	transition: all 0.2s ease-in-out;
	overflow: hidden;
}

.background-style-category-tile:hover {
	transform: translateY(-2px);
	box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.background-style-category-tile:focus {
	box-shadow: 0 0 0 2px rgb(168, 85, 247), 0 3px 10px rgba(0, 0, 0, 0.2);
}

.background-style-category-tile[aria-pressed="true"]::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 3px;
	background-color: rgb(168, 85, 247);
}

.background-solid-color-picker {
	animation: fadeDown 0.3s ease forwards;
	transform-origin: top center;
}

@keyframes fadeDown {
	from {
		opacity: 0;
		transform: translateY(-10px) scale(0.98);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

/* ===========================
   Background Controls Component 
   =========================== */
.bgCtrl-container {
	/* Main container for the BackgroundControls component */
}

.bgCtrl-header {
	/* Header with title and reset button */
}

.bgCtrl-title {
	/* "Background" heading */
}

.bgCtrl-resetBtn {
	/* Reset to Default button */
}

.bgCtrl-instructions {
	/* Instruction text below header */
}

.bgCtrl-categoryGridContainer {
	/* Grid container for category blocks */
}

/* ===========================
   Category Blocks 
   =========================== */
.bgCtrl-categoryBlock {
	/* Individual category block */
}

.bgCtrl-categoryIcon {
	/* Icon for a category */
}

.bgCtrl-categoryName {
	/* Name text for a category */
}

.bgCtrl-categoryCount {
	/* Style count for a category */
}

.category-block {
	transition: all 0.2s ease-in-out;
	position: relative;
	overflow: hidden;
}

.category-block:hover {
	transform: translateY(-2px);
}

.category-block[aria-selected="true"] {
	border-color: rgb(168, 85, 247); /* purple-500 */
	background-color: rgba(168, 85, 247, 0.2); /* purple-500 with opacity */
	box-shadow: 0 4px 12px rgba(168, 85, 247, 0.25);
}

.category-block[aria-selected="true"]::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	height: 2px;
	width: 100%;
	background-color: rgb(168, 85, 247);
}

/* ===========================
   Solid Color Panel
   =========================== */
.bgCtrl-solidColorContainer {
	/* Container for solid color options when expanded */
}

.bgCtrl-solidColorHeader {
	/* Header for solid color section */
}

.bgCtrl-hideColors {
	/* Hide colors button */
}

.bgCtrl-colorGridContainer {
	/* Grid for color swatches */
}

.bgCtrl-colorSwatch {
	/* Individual color swatch */
}

.bgCtrl-colorPreview {
	/* Preview section of a color swatch */
}

.bgCtrl-colorSwatchInfo {
	/* Container for color name and selected indicator */
}

.bgCtrl-colorName {
	/* Text label for a color */
}

.bgCtrl-selectedIndicator {
	/* Icon container that shows when a color is selected */
}

.bgCtrl-checkIcon {
	/* Check icon for selected state */
}

.bgCtrl-customColorCard {
	/* Card for custom color picker */
}

.bgCtrl-customColorLabel {
	/* Label for custom color option */
}

.bgCtrl-colorPickerInput {
	/* Color picker input element */
}

.bgCtrl-selectCustomBtn {
	/* Button to select custom color */
}

/* ===========================
   Modal Components
   =========================== */
.bgModal-backdrop {
	z-index: 50 !important;
}

.bgModal-container {
	z-index: 60 !important;
}

.bgModal-header {
	/* Modal header */
}

.bgModal-title {
	/* Modal title */
}

.bgModal-categoryIcon {
	/* Category icon in modal header */
}

.bgModal-closeButton {
	/* Close button in header */
}

.bgModal-closeIcon {
	/* Close icon */
}

.bgModal-body {
	/* Modal body content */
}

.bgModal-description {
	/* Description text at top of modal */
}

.bgModal-styleGrid {
	/* Grid of style cards */
}

.bgModal-styleCard {
	/* Individual style card */
}

.bgModal-selectedIndicator {
	/* Check indicator on selected style */
}

.bgModal-checkIcon {
	/* Check icon */
}

.bgModal-previewContainer {
	/* Container for preview image */
}

.bgModal-previewImage {
	/* Preview image */
}

.bgModal-noPreview {
	/* Placeholder when no preview is available */
}

.bgModal-styleInfo {
	/* Container for style name and description */
}

.bgModal-styleName {
	/* Style name */
}

.bgModal-styleDescription {
	/* Style description */
}

.bgModal-showMoreCard {
	/* Show more card at the end of limited grid */
}

.bgModal-showMoreIcon {
	/* Plus icon for show more */
}

.bgModal-showMoreText {
	/* Show more text */
}

.bgModal-footer {
	/* Modal footer */
}

.bgModal-footerCloseBtn {
	/* Close button in footer */
}

/* Generic style card styling */
.style-preview-card {
	overflow: hidden;
	transition: all 0.2s ease;
	display: flex;
	flex-direction: column;
}

.style-preview-card:hover {
	transform: translateY(-2px);
}

.style-preview-card img {
	transition: transform 0.3s ease;
}

.show-more-card {
	transition: all 0.2s ease;
}

.show-more-card:hover {
	transform: translateY(-2px);
	background-color: rgba(107, 114, 128, 0.7); /* gray-500 with opacity */
}

/* Custom color picker styling */
.color-picker-card input[type="color"] {
	-webkit-appearance: none;
	border: none;
	cursor: pointer;
}

.color-picker-card input[type="color"]::-webkit-color-swatch-wrapper {
	padding: 0;
}

.color-picker-card input[type="color"]::-webkit-color-swatch {
	border: 1px solid rgb(75, 85, 99); /* gray-600 */
	border-radius: 0.375rem; /* rounded-md */
}

/* Color swatch styling */
.color-swatch {
	transition: all 0.2s ease;
}

.color-swatch:hover {
	transform: translateY(-2px);
}

/* Modal animation */
@keyframes modalFadeIn {
	from {
		opacity: 0;
		transform: scale(0.95) translateY(10px);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

.animate-modalFadeIn {
	animation: modalFadeIn 0.2s ease-out forwards;
}

/* Toggle row layout */
.toggle-row{
	display:flex;
	align-items:center;
	justify-content:space-between;
	gap:1rem;
	width:100%;
}
.toggle-label{flex:1;min-width:0;}
.toggle-switch{flex-shrink:0;} 