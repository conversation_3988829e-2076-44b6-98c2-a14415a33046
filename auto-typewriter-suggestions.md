---
title: auto-typewriter-suggestions
id: auto-typewriter-suggestions.md
feature: prompt-field-placeholder-rotation
---

# ✨ Auto Typewriter Prompt Suggestions

## 📌 Overview
Help beginner users generate compelling YouTube video titles by auto-rotating curated examples inside the prompt input field with an engaging typewriter animation effect. This feature provides inspiration while enhancing the UX with a premium feel.

---

## 🛠️ Implementation Details

### Core Functionality
- When the prompt input is empty, a **rotating placeholder animation** displays suggested title examples.
- Prompts are displayed with a smooth, letter-by-letter typing animation (typewriter effect).
- Completed prompts pause briefly before gracefully erasing and transitioning to the next example.
- Animation stops immediately when the user:
  - Starts typing in the input field
  - Clicks/focuses on the input field
  - Has reduced motion preferences enabled

### Technical Specifications
- **Animation timing:**
  - Typing speed: 50ms per character
  - Hold duration: 2000ms after typing completes
  - Pause between examples: 300ms
  - Erasure speed: 25ms per character (2x typing speed)
  
- **React implementation:**
  - Uses React hooks (useState, useEffect, useRef)
  - Custom utility in `src/utils/typewriterSuggestions.js`
  - Animation runs client-side using `setInterval`
  - Smooth transitions with CSS for fade effects
  - Respects accessibility best practices

- **Accessibility:**
  - Honors `prefers-reduced-motion` system setting
  - Provides visual cue but doesn't interfere with normal input
  - Cleans up all intervals on unmount to prevent memory leaks

---

## 📋 Example Suggestions

```plain
Trying to live on $10 a day 💸
Reacting to my old cringy videos 😳
Epic win moments in Fortnite 🎮
How to edit videos for beginners 🎞️
I built an app in 24 hours 💻
My 30-day fitness transformation 💪
Exploring hidden gems in my city 🏙️
Top 10 budget travel hacks ✈️
```

*Suggestions are kept concise, engaging, and emoji-enhanced to inspire users.*

---

## 💡 UX Details

- **Visual feedback:**
  - Blinking cursor effect at the end of typed text
  - Subtle text color to distinguish from user input
  - Smooth enter/exit transitions

- **User interactions:**
  - Animation pauses on focus, resumes on blur if input remains empty
  - "Need ideas?" helper text appears with lightbulb icon when field is empty
  - Animation respects user's `prefers-reduced-motion` setting

---

## 🧪 Testing Guidelines

### Test cases:
1. Animation starts automatically on page load if input is empty
2. Animation stops when user starts typing
3. Animation stops when focusing on the input
4. Animation resumes when blurring empty input
5. Animation doesn't restart if user has entered and deleted text
6. Compatible across browsers (Chrome, Firefox, Safari, Edge)
7. Mobile-friendly behavior on touch devices

---

## 📊 Success Metrics

- **Primary:** Increased user engagement (measured by % of users who enter content)
- **Secondary:** Reduced time-to-first-input for new users 
- **Qualitative:** Positive feedback on UX improvement

---

**Implementation Status:** Integrated in `/src/utils/typewriterSuggestions.js` and referenced in `PromptInput` component. 