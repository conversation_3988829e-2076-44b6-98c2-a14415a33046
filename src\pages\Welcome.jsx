// Import React from the installed packages
const React = window.React;
const { useState, useRef } = React;
import { supabase } from '../utils/supabaseClient.js';

/**
 * Welcome Screen Component - Handles Signup and Login 
 * Styled with Hero UI Kit (Tailwind CDN)
 * 
 * This component provides a welcome screen with signup/login functionality
 * using Supabase authentication including email and Google OAuth.
 * 
 * @param {Object} props
 * @param {Function} props.onAuthenticated - Callback for when auth is completed
 */
const Welcome = ({ onAuthenticated }) => {
    // State to toggle between login and signup forms
    const [isLogin, setIsLogin] = useState(false);
    
    // State for form inputs
    const [formData, setFormData] = useState({
        fullName: '',
        email: '',
        password: '',
        resetEmail: ''  // For forgot password flow
    });
    
    // State for validation errors
    const [errors, setErrors] = useState({
        fullName: '',
        email: '',
        password: '',
        resetEmail: ''
    });

    // Touched fields tracking for validation
    const [touched, setTouched] = useState({
        fullName: false,
        email: false,
        password: false,
        resetEmail: false
    });
    
    // Refs for form fields (used for focusing on error)
    const inputRefs = {
        fullName: useRef(null),
        email: useRef(null),
        password: useRef(null),
        resetEmail: useRef(null)
    };
    
    // State for password visibility
    const [showPassword, setShowPassword] = useState(false);

    // State for forgot password flow
    const [isForgotPassword, setIsForgotPassword] = useState(false);
    const [resetStep, setResetStep] = useState(1); // 1 = email entry, 2 = confirmation
    const [isSubmitting, setIsSubmitting] = useState(false); // For loading state
    
    // State for auth errors from Supabase
    const [authError, setAuthError] = useState('');
    
    // Validation functions
    const validateEmail = (email) => {
        // Check if email is only numbers
        if (/^\d+$/.test(email)) {
            return "Email cannot contain only numbers";
        }
        
        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return "Please enter a valid email address";
        }
        
        return "";
    };
    
    const validateFullName = (name) => {
        if (!name) return "";
        
        // Check if name contains numbers
        if (/\d/.test(name)) {
            return "Name cannot contain numbers";
        }
        
        // Check if name is too short
        if (name.trim().length < 2) {
            return "Name must be at least 2 characters";
        }
        
        return "";
    };
    
    // Password validation function
    const validatePassword = (password) => {
        if (!password) return "";
        
        // Check if password is at least 6 characters
        if (password.length < 6) {
            return "Password must be at least 6 characters";
        }
        
        return "";
    };
    
    // Handle form input changes with validation
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        
        // Clear auth error when user starts typing
        setAuthError('');
        
        // Update form data
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        
        // Mark field as touched
        if (!touched[name]) {
            setTouched(prev => ({
                ...prev,
                [name]: true
            }));
        }
        
        // Validate field
        let errorMessage = "";
        if (name === 'email' || name === 'resetEmail') {
            errorMessage = validateEmail(value);
        } else if (name === 'fullName') {
            errorMessage = validateFullName(value);
        } else if (name === 'password') {
            errorMessage = validatePassword(value);
        }
        
        // Update error state
        setErrors(prev => ({
            ...prev,
            [name]: errorMessage
        }));
    };
    
    // Handle input blur for validation
    const handleBlur = (e) => {
        const { name } = e.target;
        
        // Mark field as touched
        setTouched(prev => ({
            ...prev,
            [name]: true
        }));
        
        // Validate on blur to ensure we catch errors even if onChange didn't fire
        const value = formData[name];
        let errorMessage = "";
        
        if (name === 'email' || name === 'resetEmail') {
            errorMessage = validateEmail(value);
        } else if (name === 'fullName') {
            errorMessage = validateFullName(value);
        } else if (name === 'password') {
            errorMessage = validatePassword(value);
        }
        
        setErrors(prev => ({
            ...prev,
            [name]: errorMessage
        }));
    };
    
    // Check if the form has any validation errors
    const hasErrors = () => {
        // For login form, only check email and password
        if (isLogin) {
            return !!errors.email || !!errors.password;
        }
        
        // For signup form, check all fields
        return !!errors.email || !!errors.fullName || !!errors.password;
    };
    
    // Handle form submission with Supabase
    const handleSubmit = async (e) => {
        e.preventDefault();
        
        // Mark all fields as touched
        const newTouched = {};
        Object.keys(formData).forEach(key => {
            newTouched[key] = true;
        });
        setTouched(newTouched);
        
        // Validate all fields
        const newErrors = {
            email: validateEmail(formData.email),
            fullName: !isLogin ? validateFullName(formData.fullName) : "",
            password: validatePassword(formData.password),
            resetEmail: ""
        };
        
        setErrors(newErrors);
        
        // Check if there are any errors
        const hasValidationErrors = Object.values(newErrors).some(error => !!error);
        
        if (hasValidationErrors) {
            // Focus the first field with an error
            if (newErrors.fullName && !isLogin) {
                inputRefs.fullName.current?.focus();
            } else if (newErrors.email) {
                inputRefs.email.current?.focus();
            } else if (newErrors.password) {
                inputRefs.password.current?.focus();
            }
            
            return;
        }
        
        setIsSubmitting(true);
        setAuthError('');
        
        try {
            if (isLogin) {
                // Sign in with Supabase
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: formData.email,
                    password: formData.password,
                });
                
                if (error) {
                    console.error('Login error:', error);
                    setAuthError(error.message);
                    setIsSubmitting(false);
                    return;
                }
                
                // Successful login
                console.log('Login successful:', data);
                onAuthenticated();
            } else {
                // Sign up with Supabase
                const { data, error } = await supabase.auth.signUp({
                    email: formData.email,
                    password: formData.password,
                    options: {
                        data: {
                            full_name: formData.fullName,
                        }
                    }
                });
                
                if (error) {
                    console.error('Signup error:', error);
                    setAuthError(error.message);
                    setIsSubmitting(false);
                    return;
                }
                
                // Check if email confirmation is required
                if (data.user && !data.session) {
                    setAuthError('Please check your email to confirm your account before logging in.');
                    setIsSubmitting(false);
                    // Switch to login mode
                    setIsLogin(true);
                    return;
                }
                
                // Successful signup and auto-login
                console.log('Signup successful:', data);
                onAuthenticated();
            }
        } catch (error) {
            console.error('Authentication error:', error);
            setAuthError('An unexpected error occurred. Please try again.');
            setIsSubmitting(false);
        }
    };
    
    // Handle Google OAuth login
    const handleGoogleLogin = async () => {
        setIsSubmitting(true);
        setAuthError('');
        
        try {
            const { data, error } = await supabase.auth.signInWithOAuth({
                provider: 'google',
                options: {
                    redirectTo: window.location.origin,
                }
            });
            
            if (error) {
                console.error('Google login error:', error);
                setAuthError(error.message);
                setIsSubmitting(false);
                return;
            }
            
            // Note: OAuth will redirect the page, so we won't reach here
            console.log('Google OAuth initiated:', data);
        } catch (error) {
            console.error('Google login error:', error);
            setAuthError('Failed to initiate Google login. Please try again.');
            setIsSubmitting(false);
        }
    };
    
    // Handle forgot password form submission
    const handleResetSubmit = async (e) => {
        e.preventDefault();
        
        // Validate the reset email
        const resetEmailError = validateEmail(formData.resetEmail);
        setErrors(prev => ({
            ...prev,
            resetEmail: resetEmailError
        }));
        
        // Mark as touched
        setTouched(prev => ({
            ...prev,
            resetEmail: true
        }));
        
        if (resetEmailError) {
            inputRefs.resetEmail.current?.focus();
            return;
        }
        
        setIsSubmitting(true);
        setAuthError('');
        
        try {
            const { data, error } = await supabase.auth.resetPasswordForEmail(formData.resetEmail, {
                redirectTo: `${window.location.origin}/reset-password`,
            });
            
            if (error) {
                console.error('Password reset error:', error);
                setAuthError(error.message);
                setIsSubmitting(false);
                return;
            }
            
            // Move to confirmation step
            setResetStep(2);
            setIsSubmitting(false);
        } catch (error) {
            console.error('Password reset error:', error);
            setAuthError('Failed to send reset email. Please try again.');
            setIsSubmitting(false);
        }
    };
    
    // Skip authentication and go to main app
    const handleSkip = () => {
        console.log('Skipping authentication');
        // Call the authentication callback to proceed to main app
        onAuthenticated();
    };

    // Back to login from forgot password flow
    const handleBackToLogin = () => {
        setIsForgotPassword(false);
        setResetStep(1); // Reset to first step for next time
        setAuthError(''); // Clear any errors
    };

    // Resend password reset email
    const handleResend = () => {
        setResetStep(1); // Go back to email entry step
        setAuthError(''); // Clear any errors
    };

    // Render the forgot password flow
    const renderForgotPasswordFlow = () => {
        if (resetStep === 1) {
            // Step 1: Email entry
            return (
                <>
                    <div className="text-center mb-8">
                        <h2 className="text-3xl font-bold mb-2">Reset your password</h2>
                        <p className="text-gray-400">Enter your email address and we'll send you a password reset link.</p>
                    </div>
                    
                    <form onSubmit={handleResetSubmit} className="space-y-6">
                        <div>
                            <label htmlFor="resetEmail" className="block text-sm font-medium text-gray-300 mb-1">
                                Email Address
                            </label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="iconify text-gray-400" data-icon="solar:letter-linear"></span>
                                </div>
                                <input
                                    type="email"
                                    id="resetEmail"
                                    name="resetEmail"
                                    value={formData.resetEmail}
                                    onChange={handleInputChange}
                                    onBlur={handleBlur}
                                    ref={inputRefs.resetEmail}
                                    className={`w-full pl-10 pr-4 py-3 bg-gray-800 border ${errors.resetEmail && touched.resetEmail ? 'border-red-500' : 'border-gray-700'} rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent placeholder-gray-500 text-white`}
                                    placeholder="Enter your email"
                                    required
                                    aria-invalid={!!errors.resetEmail}
                                    aria-describedby={errors.resetEmail ? "resetEmail-error" : undefined}
                                />
                                {errors.resetEmail && touched.resetEmail && (
                                    <span className="absolute right-3 top-3 text-red-500">
                                        <span className="iconify" data-icon="solar:danger-circle-bold"></span>
                                    </span>
                                )}
                            </div>
                            {errors.resetEmail && touched.resetEmail && (
                                <p id="resetEmail-error" className="mt-1 text-xs text-red-500 flex items-center gap-1" aria-live="polite">
                                    <span className="iconify" data-icon="solar:danger-circle-bold"></span>
                                    {errors.resetEmail}
                                </p>
                            )}
                        </div>
                        
                        <button
                            type="submit"
                            className={`w-full py-3 px-4 ${errors.resetEmail && touched.resetEmail ? 'bg-purple-600/70 cursor-not-allowed' : 'bg-purple-600 hover:bg-purple-700'} text-white font-medium rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200 flex items-center justify-center`}
                            disabled={isSubmitting || (errors.resetEmail && touched.resetEmail)}
                        >
                            {isSubmitting ? (
                                <>
                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Sending Reset Link...
                                </>
                            ) : "Send Reset Link"}
                        </button>
                        
                        {errors.resetEmail && touched.resetEmail && (
                            <div className="mt-2 text-xs text-red-500 flex items-center gap-1 justify-center" aria-live="polite">
                                <span className="iconify" data-icon="solar:danger-circle-bold"></span>
                                Please fix the email error before continuing.
                            </div>
                        )}
                        
                        {/* Auth Error Display for Reset */}
                        {authError && (
                            <div className="mt-4 py-3 px-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                                <p className="text-sm text-red-500 flex items-start gap-2" aria-live="polite">
                                    <span className="iconify flex-shrink-0 mt-0.5" data-icon="solar:danger-circle-bold"></span>
                                    <span>{authError}</span>
                                </p>
                            </div>
                        )}
                    </form>
                    
                    <button
                        onClick={handleBackToLogin}
                        className="mt-6 w-full text-center text-sm text-gray-400 hover:text-purple-300 transition-colors"
                    >
                        Back to Login
                    </button>
                </>
            );
        } else {
            // Step 2: Confirmation
            return (
                <>
                    <div className="text-center mb-8">
                        <div className="flex justify-center mb-6">
                            <div className="w-16 h-16 rounded-full bg-purple-500/20 flex items-center justify-center">
                                <span className="iconify text-3xl text-purple-400" data-icon="solar:inbox-check-linear"></span>
                            </div>
                        </div>
                        <h2 className="text-3xl font-bold mb-2">Check your email</h2>
                        <p className="text-gray-400">
                            We've sent a password reset link to <span className="text-purple-300">{formData.resetEmail}</span>. 
                            If you don't see it, check your spam folder.
                        </p>
                    </div>
                    
                    <button
                        onClick={handleBackToLogin}
                        className="w-full py-3 px-4 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200"
                    >
                        Back to Login
                    </button>
                    
                    <button
                        onClick={handleResend}
                        className="mt-4 w-full text-center text-sm text-gray-400 hover:text-purple-300 transition-colors"
                    >
                        Didn't receive an email? Resend
                    </button>
                </>
            );
        }
    };

    // If in forgot password flow, render that instead of login/signup
    if (isForgotPassword) {
        return (
            <div className="flex min-h-screen bg-gray-900 text-white">
                {/* Decorative left panel with gradient - keep same as main page */}
                <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-purple-900 via-indigo-800 to-blue-900 justify-center items-center p-12">
                    <div className="max-w-md">
                        <h1 className="text-4xl font-bold mb-6">GPT-4V Thumbnail Generator</h1>
                        <p className="text-xl text-gray-300 mb-8">Create stunning YouTube thumbnails with the power of AI. Perfect for content creators.</p>
                        <div className="flex gap-4">
                            <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-pink-500 to-purple-600 flex items-center justify-center">
                                <span className="iconify text-3xl" data-icon="solar:image-add-bold"></span>
                            </div>
                            <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-400 flex items-center justify-center">
                                <span className="iconify text-3xl" data-icon="solar:magic-stick-3-bold"></span>
                            </div>
                            <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-amber-500 to-orange-400 flex items-center justify-center">
                                <span className="iconify text-3xl" data-icon="solar:star-bold"></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                {/* Right panel with forgot password form */}
                <div className="w-full lg:w-1/2 flex flex-col justify-center items-center p-6 md:p-12">
                    {/* Skip button (top right) */}
                    <button 
                        onClick={handleSkip}
                        className="absolute top-6 right-6 text-sm text-gray-400 hover:text-white transition-colors duration-200"
                    >
                        Skip &rarr;
                    </button>
                    
                    {/* Back button (top left) */}
                    <button 
                        onClick={handleBackToLogin}
                        className="absolute top-6 left-6 text-sm text-gray-400 hover:text-white transition-colors duration-200 flex items-center"
                    >
                        <span className="iconify mr-1" data-icon="solar:arrow-left-linear"></span>
                        Back
                    </button>
                    
                    <div className="w-full max-w-md">
                        {renderForgotPasswordFlow()}
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="flex min-h-screen bg-gray-900 text-white">
            {/* Decorative left panel with gradient */}
            <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-purple-900 via-indigo-800 to-blue-900 justify-center items-center p-12">
                <div className="max-w-md">
                    <h1 className="text-4xl font-bold mb-6">GPT-4V Thumbnail Generator</h1>
                    <p className="text-xl text-gray-300 mb-8">Create stunning YouTube thumbnails with the power of AI. Perfect for content creators.</p>
                    <div className="flex gap-4">
                        <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-pink-500 to-purple-600 flex items-center justify-center">
                            <span className="iconify text-3xl" data-icon="solar:image-add-bold"></span>
                        </div>
                        <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-400 flex items-center justify-center">
                            <span className="iconify text-3xl" data-icon="solar:magic-stick-3-bold"></span>
                        </div>
                        <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-amber-500 to-orange-400 flex items-center justify-center">
                            <span className="iconify text-3xl" data-icon="solar:star-bold"></span>
                        </div>
                    </div>
                </div>
            </div>
            
            {/* Right panel with auth form */}
            <div className="w-full lg:w-1/2 flex flex-col justify-center items-center p-6 md:p-12">
                {/* Skip button (top right) */}
                <button 
                    onClick={handleSkip}
                    className="absolute top-6 right-6 text-sm text-gray-400 hover:text-white transition-colors duration-200"
                >
                    Skip &rarr;
                </button>
                
                <div className="w-full max-w-md">
                    {/* Heading */}
                    <div className="text-center mb-8">
                        <h2 className="text-3xl font-bold mb-2">{isLogin ? "Welcome back" : "Create account"}</h2>
                        <p className="text-gray-400">{isLogin ? "Login to your account" : "Sign up for a free account"}</p>
                    </div>
                    
                    {/* Auth Form */}
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Full Name - only for signup */}
                        {!isLogin && (
                            <div>
                                <label htmlFor="fullName" className="block text-sm font-medium text-gray-300 mb-1">
                                    Full Name
                                </label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span className="iconify text-gray-400" data-icon="solar:user-linear"></span>
                                    </div>
                                    <input
                                        type="text"
                                        id="fullName"
                                        name="fullName"
                                        value={formData.fullName}
                                        onChange={handleInputChange}
                                        onBlur={handleBlur}
                                        ref={inputRefs.fullName}
                                        className={`w-full pl-10 pr-4 py-3 bg-gray-800 border ${errors.fullName && touched.fullName ? 'border-red-500' : 'border-gray-700'} rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent placeholder-gray-500 text-white`}
                                        placeholder="Enter your name"
                                        required
                                        aria-invalid={!!errors.fullName}
                                        aria-describedby={errors.fullName ? "fullName-error" : undefined}
                                    />
                                    {errors.fullName && touched.fullName && (
                                        <span className="absolute right-3 top-3 text-red-500">
                                            <span className="iconify" data-icon="solar:danger-circle-bold"></span>
                                        </span>
                                    )}
                                </div>
                                {errors.fullName && touched.fullName && (
                                    <p id="fullName-error" className="mt-1 text-xs text-red-500 flex items-center gap-1" aria-live="polite">
                                        <span className="iconify" data-icon="solar:danger-circle-bold"></span>
                                        {errors.fullName}
                                    </p>
                                )}
                            </div>
                        )}
                        
                        {/* Email */}
                        <div>
                            <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                                Email Address
                            </label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="iconify text-gray-400" data-icon="solar:letter-linear"></span>
                                </div>
                                <input
                                    type="email"
                                    id="email"
                                    name="email"
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    onBlur={handleBlur}
                                    ref={inputRefs.email}
                                    className={`w-full pl-10 pr-4 py-3 bg-gray-800 border ${errors.email && touched.email ? 'border-red-500' : 'border-gray-700'} rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent placeholder-gray-500 text-white`}
                                    placeholder="Enter your email"
                                    required
                                    aria-invalid={!!errors.email}
                                    aria-describedby={errors.email ? "email-error" : undefined}
                                />
                                {errors.email && touched.email && (
                                    <span className="absolute right-3 top-3 text-red-500">
                                        <span className="iconify" data-icon="solar:danger-circle-bold"></span>
                                    </span>
                                )}
                            </div>
                            {errors.email && touched.email && (
                                <p id="email-error" className="mt-1 text-xs text-red-500 flex items-center gap-1" aria-live="polite">
                                    <span className="iconify" data-icon="solar:danger-circle-bold"></span>
                                    {errors.email}
                                </p>
                            )}
                        </div>
                        
                        {/* Password */}
                        <div>
                            <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
                                Password
                            </label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="iconify text-gray-400" data-icon="solar:lock-keyhole-linear"></span>
                                </div>
                                <input
                                    type={showPassword ? "text" : "password"}
                                    id="password"
                                    name="password"
                                    value={formData.password}
                                    onChange={handleInputChange}
                                    onBlur={handleBlur}
                                    ref={inputRefs.password}
                                    className={`w-full pl-10 pr-12 py-3 bg-gray-800 border ${errors.password && touched.password ? 'border-red-500' : 'border-gray-700'} rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent placeholder-gray-500 text-white`}
                                    placeholder={isLogin ? "Enter password" : "Create password (min. 6 characters)"}
                                    required
                                    aria-invalid={!!errors.password}
                                    aria-describedby={errors.password ? "password-error" : undefined}
                                />
                                <button
                                    type="button"
                                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                    onClick={() => setShowPassword(!showPassword)}
                                >
                                    <span 
                                        className="iconify text-gray-400 hover:text-white transition-colors" 
                                        data-icon={showPassword ? "solar:eye-closed-linear" : "solar:eye-linear"}
                                    ></span>
                                </button>
                            </div>
                            {errors.password && touched.password && (
                                <p id="password-error" className="mt-1 text-xs text-red-500 flex items-center gap-1" aria-live="polite">
                                    <span className="iconify" data-icon="solar:danger-circle-bold"></span>
                                    {errors.password}
                                </p>
                            )}
                            {isLogin && (
                                <div className="flex justify-end mt-1">
                                    <button 
                                        type="button"
                                        onClick={() => setIsForgotPassword(true)}
                                        className="text-xs text-purple-400 hover:text-purple-300"
                                    >
                                        Forgot password?
                                    </button>
                                </div>
                            )}
                        </div>
                        
                        {/* Submit Button */}
                        <button
                            type="submit"
                            className={`w-full py-3 px-4 ${hasErrors() || isSubmitting ? 'bg-purple-600/70 cursor-not-allowed' : 'bg-purple-600 hover:bg-purple-700'} text-white font-medium rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200 flex items-center justify-center`}
                            disabled={hasErrors() || isSubmitting}
                        >
                            {isSubmitting ? (
                                <>
                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    {isLogin ? "Logging in..." : "Creating account..."}
                                </>
                            ) : (isLogin ? "Log in" : "Sign up")}
                        </button>
                        
                        {/* Auth Error Display */}
                        {authError && (
                            <div className="py-3 px-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                                <p className="text-sm text-red-500 flex items-start gap-2" aria-live="polite">
                                    <span className="iconify flex-shrink-0 mt-0.5" data-icon="solar:danger-circle-bold"></span>
                                    <span>{authError}</span>
                                </p>
                            </div>
                        )}
                        
                        {/* Validation Error summary (if any) */}
                        {(errors.email && touched.email) || (errors.fullName && touched.fullName && !isLogin) || (errors.password && touched.password) ? (
                            <div className="py-2 px-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                                <p className="text-xs text-red-500 flex items-start gap-1.5" aria-live="polite">
                                    <span className="iconify flex-shrink-0 mt-0.5" data-icon="solar:danger-circle-bold"></span>
                                    <span>Please fix the errors above before continuing.</span>
                                </p>
                            </div>
                        ) : null}
                        
                        {/* Divider */}
                        <div className="relative py-2">
                            <div className="absolute inset-0 flex items-center">
                                <div className="w-full border-t border-gray-700"></div>
                            </div>
                            <div className="relative flex justify-center">
                                <span className="bg-gray-900 px-4 text-sm text-gray-400">
                                    or continue with
                                </span>
                            </div>
                        </div>
                        
                        {/* Google Login Button */}
                        <button
                            type="button"
                            className="w-full py-2.5 px-4 bg-white hover:bg-gray-50 text-gray-800 font-medium rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300 border border-gray-300 transition-all duration-200 flex items-center justify-center space-x-2 relative hover:opacity-90"
                            aria-label="Log in with Google"
                            onClick={handleGoogleLogin}
                        >
                            <svg width="18" height="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
                                <path fill="#4285F4" d="M45.12 24.5c0-1.56-.14-3.06-.4-4.5H24v8.51h11.84c-.51 2.75-2.06 5.08-4.39 6.64v5.52h7.11c4.16-3.83 6.56-9.47 6.56-16.17z"/>
                                <path fill="#34A853" d="M24 46c5.94 0 10.92-1.97 14.56-5.33l-7.11-5.52c-1.97 1.32-4.49 2.1-7.45 2.1-5.73 0-10.58-3.87-12.31-9.07H4.34v5.7C7.96 41.07 15.4 46 24 46z"/>
                                <path fill="#FBBC05" d="M11.69 28.18C11.25 26.86 11 25.45 11 24s.25-2.86.69-4.18v-5.7H4.34C2.85 17.09 2 20.45 2 24c0 3.55.85 6.91 2.34 9.88l7.35-5.7z"/>
                                <path fill="#EA4335" d="M24 10.75c3.23 0 6.13 1.11 8.41 3.29l6.31-6.31C34.91 4.18 29.93 2 24 2 15.4 2 7.96 6.93 4.34 14.12l7.35 5.7c1.73-5.2 6.58-9.07 12.31-9.07z"/>
                            </svg>
                            <span>{isLogin ? "Log in with Google" : "Sign up with Google"}</span>
                        </button>
                    </form>
                    
                    {/* Toggle between login/signup */}
                    <p className="mt-8 text-center text-sm text-gray-400">
                        {isLogin ? "Don't have an account?" : "Already have an account?"}
                        {" "}
                        <button 
                            onClick={() => {
                                setIsLogin(!isLogin);
                                // Reset form errors and touched states when switching modes
                                setErrors({
                                    fullName: '',
                                    email: '',
                                    password: '',
                                    resetEmail: ''
                                });
                                setTouched({
                                    fullName: false,
                                    email: false,
                                    password: false,
                                    resetEmail: false
                                });
                            }}
                            className="text-purple-400 hover:text-purple-300 font-medium focus:outline-none"
                        >
                            {isLogin ? "Sign up" : "Log in"}
                        </button>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default Welcome; 