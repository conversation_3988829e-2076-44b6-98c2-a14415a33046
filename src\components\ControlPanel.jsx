// Named export for ControlPanel component
import { BackgroundControls } from './background/BackgroundControls.jsx';
import { CollapsibleSection } from './ui/CollapsibleSection.jsx';
import '../styles/background-controls.css'; // Import the new CSS file

// Fixed positioning tooltip system - escapes all container boundaries
// TEMPORARILY SIMPLIFIED FOR DEBUGGING TOGGLE CONFLICT
const createTooltipIcon = (tooltipText, tooltipId = '') => {
    // const iconRef = React.useRef(null);
    // const tooltipRef = React.useRef(null);
    // const [isVisible, setIsVisible] = React.useState(false);
    // const [position, setPosition] = React.useState({ top: 0, left: 0, placement: 'top' });

    // const calculatePosition = React.useCallback(() => {
    //     // ... original calculation logic removed ...
    // }, []);

    // const handleMouseEnter = () => {
    //     // setIsVisible(true);
    //     // requestAnimationFrame(() => {
    //     //     calculatePosition();
    //     // });
    // };

    // const handleMouseLeave = () => {
    //     // setIsVisible(false);
    // };

    // const handleFocus = () => {
    //     // setIsVisible(true);
    //     // requestAnimationFrame(() => {
    //     //     calculatePosition();
    //     // });
    // };

    // const handleBlur = () => {
    //     // setIsVisible(false);
    // };

    // React.useEffect(() => {
    //     // ... original effect with scroll/resize listeners removed ...
    // }, [isVisible, calculatePosition]);

    // const getArrowStyles = () => {
    //     // ... original arrow logic removed ...
    //     return {};
    // };

    return React.createElement('div', {
        className: 'tooltip-icon-container relative', // Keep basic container for structure
        tabIndex: 0,
        'aria-label': 'Information about this setting (tooltip temporarily disabled)',
        role: 'button'
    },
        React.createElement('svg', {
            // ref: iconRef, // Ref removed
            xmlns: 'http://www.w3.org/2000/svg',
            fill: 'none',
            viewBox: '0 0 24 24',
            stroke: 'currentColor',
            className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
        },
            React.createElement('path', {
                strokeLinecap: 'round',
                strokeLinejoin: 'round',
                strokeWidth: 2,
                d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
            })
        )
        // Tooltip div temporarily removed
        // isVisible && React.createElement('div', {
        //     ref: tooltipRef,
        //     className: `tooltip-fixed-content fixed bg-gray-900 text-white text-xs rounded-lg shadow-xl p-3 transition-opacity duration-200 pointer-events-none`,
        //     style: {
        //         top: `0px`, // Simplified style
        //         left: `0px`, // Simplified style
        //         zIndex: 9999,
        //         maxWidth: '256px',
        //         opacity: 0, // Simplified style
        //         visibility: 'hidden' // Simplified style
        //     },
        //     role: 'tooltip',
        //     'aria-hidden': 'true',
        //     id: tooltipId
        // },
        //     React.createElement('p', null, tooltipText)
        // )
    );
};

export const ControlPanel = ({
    includePerson,
    includeIcons,
    textOverlay,
    mood,
    handleToggleChange,
    handleMoodChange,
    setIncludePerson, // Direct setters passed from App
    setIncludeIcons,
    setTextOverlay,
    selectedTextSize, // Added prop
    setSelectedTextSize, // Added prop
    fitFullCanvas, // Added prop
    setFitFullCanvas, // Added prop
    backgroundCustomizationEnabled, // NEW: Toggle for background section
    setBackgroundCustomizationEnabled, // NEW: Setter for background toggle
    selectedBackgroundType, // NEW: For background styling
    selectedBackgroundStyleId, // NEW: For background template ID
    handleBackgroundStyleSelect, // NEW: Handler for selecting background style
    selectedSolidBgColor, // NEW: For solid color background
    handleSolidBgColorChange, // NEW: Handler for changing solid color
    // For person settings
    selectedExpression,
    setSelectedExpression,
    selectedGender,
    setSelectedGender,
    customFaceImageUrl,
    setCustomFaceImageUrl,
    imageSourceType,
    handleImageSourceTypeChange,
    handleFileUpload,
    setErrorMsg,
    // For text settings
    overlayText,
    setOverlayText,
    isEditingOverlayText,
    setIsEditingOverlayText,
    textPosition,
    setTextPosition,
    selectedFontFamily,
    setSelectedFontFamily,
    primaryTextColor,
    setPrimaryTextColor,
    secondaryTextColor,
    setSecondaryTextColor,
    selectedPalette,
    setSelectedPalette,
    // For layout and display settings
    showLayoutSimulator,
    setShowLayoutSimulator,
    showSafeZone,
    setShowSafeZone
}) => {

    // Helper function to create a toggle switch
    const createToggle = (label, id, checked, setter, tooltip = null) => {
        const handleKeyDown = (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                handleToggleChange(setter);
                e.preventDefault();
            }
        };
        return React.createElement('div', { className: 'toggle-row flex items-center justify-between py-2', id: `toggle-${id}-row` },
            React.createElement('div', { className: 'flex items-center gap-2' },
                React.createElement('label', { htmlFor: id, className: 'toggle-label text-sm font-medium text-gray-300 cursor-pointer' }, label),
                tooltip && createTooltipIcon(tooltip, `tooltip-${id}`)
            ),
            React.createElement('button', {
                id,
                role: 'switch',
                'aria-checked': checked,
                onClick: () => handleToggleChange(setter),
                onKeyDown: handleKeyDown,
                tabIndex: '0',
                className: `toggle-switch ${checked ? 'bg-purple-600' : 'bg-gray-600'} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900`
            },
                React.createElement('span', { className: 'sr-only' }, 'Use setting'),
                React.createElement('span', {
                    'aria-hidden': 'true',
                    className: `toggle-circle ${checked ? 'translate-x-5' : 'translate-x-0'} inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`
                })
            )
        );
    };

    // Helper to create Text Size Radio Buttons
    const createTextSizeSelector = () => {
        const sizes = ["Small", "Medium", "Large"];
        const label = "Text Size:";
        const groupId = "cp-textSize-radioGroup";

        return (
            React.createElement('div', { className: 'py-2', id: 'cp-textSize-row' },
                React.createElement('label', { className: 'block text-sm font-medium text-gray-300 mb-1' }, label),
                React.createElement('div', { role: 'radiogroup', id: groupId, className: 'flex space-x-4' },
                    sizes.map(size => {
                        const optionId = `cp-textSize-radio${size}`;
                        const isChecked = selectedTextSize === size;
                        return (
                            React.createElement('div', { key: size, className: 'flex items-center' },
                                React.createElement('input', {
                                    type: 'radio',
                                    id: optionId,
                                    name: groupId,
                                    value: size,
                                    checked: isChecked,
                                    onChange: (e) => setSelectedTextSize(e.target.value),
                                    className: 'h-4 w-4 text-purple-600 border-gray-500 focus:ring-purple-500 bg-gray-700 cursor-pointer'
                                }),
                                React.createElement('label', { htmlFor: optionId, className: 'ml-2 block text-sm text-gray-300 cursor-pointer' }, size)
                            )
                        );
                    })
                )
            )
        );
    };

    // Create Mood Dropdown
    const createMoodDropdown = () => {
        const moods = ['Happy', 'Excited', 'Serious', 'Surprised']; // Available moods
        return (
            React.createElement('div', { className: 'py-2' }, // Added padding
                React.createElement('label', {
                    htmlFor: 'moodSelect',
                    className: 'block text-sm font-medium text-gray-300 mb-1'
                }, 'Select Mood (if person included):'),
                React.createElement('select', {
                    id: 'moodSelect',
                    value: mood,
                    onChange: handleMoodChange,
                    disabled: !includePerson, // Disable if includePerson is false
                    className: 'w-full p-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm text-gray-100 focus:ring-purple-500 focus:border-purple-500 disabled:opacity-50 disabled:cursor-not-allowed' // Added disabled styles
                },
                    moods.map(m => React.createElement('option', { key: m, value: m }, m))
                )
            )
        );
    };

    // Create face upload section
    const createFaceUploadSection = () => {
        if (!includePerson) return null;
    
        // Use a ref for the file input to programmatically clear it
        const fileInputRef = React.useRef(null);
    
        const currentTempUrl = imageSourceType === 'url' ? customFaceImageUrl : ''; // Only prefill if type is URL and there's a URL
        const [tempUrl, setTempUrl] = React.useState(currentTempUrl);
    
        React.useEffect(() => {
            // If switching to URL mode and customFaceImageUrl is a data URL (from upload), clear tempUrl
            if (imageSourceType === 'url' && customFaceImageUrl && customFaceImageUrl.startsWith('data:image')) {
                setTempUrl('');
            } else if (imageSourceType === 'url') {
                setTempUrl(customFaceImageUrl || '');
            }
            // If switching to upload, tempUrl is not directly used for display of the uploaded image path
        }, [imageSourceType, customFaceImageUrl]);
    
        const handleSetFaceImageFromUrl = () => {
            if (tempUrl.trim() && imageSourceType === 'url') {
                // Basic URL validation: accept any http(s) URL; still warn if not starting with http(s)
                const urlTrimmed = tempUrl.trim();
                if (!/^https?:\/\//i.test(urlTrimmed)) {
                    setErrorMsg("Invalid image URL. Must start with http:// or https://");
                    return;
                }
                setCustomFaceImageUrl(tempUrl.trim());
                setErrorMsg('');
            }
        };
    
        const handleRemoveFaceImage = () => {
            setCustomFaceImageUrl('');
            setTempUrl('');
            if (fileInputRef.current) {
                fileInputRef.current.value = null; // Clear the file input
            }
            setErrorMsg('');
        };
        
        const handleUrlInputChange = (e) => {
            setTempUrl(e.target.value);
        };
    
        const tooltipText = "Replace AI face: Upload your headshot or paste an image URL (JPEG/PNG, 2MB max).";
    
        return React.createElement('div', { className: 'face-upload-section py-2', id: 'face-upload-section' },
            React.createElement('div', { className: 'flex items-center justify-between mb-2'},
                React.createElement('label', { className: 'text-sm font-medium text-gray-300' }, '🧑 Custom Face Image Source:'),
                createTooltipIcon(tooltipText, 'tooltip-face-source')
            ),
            // Radio buttons for source type
            React.createElement('div', { className: 'flex items-center gap-4 mb-3' },
                React.createElement('label', { htmlFor: 'sourceTypeUpload', className: 'flex items-center cursor-pointer text-sm text-gray-300' },
                    React.createElement('input', {
                        type: 'radio',
                        id: 'sourceTypeUpload',
                        name: 'imageSourceType',
                        value: 'upload',
                        checked: imageSourceType === 'upload',
                        onChange: () => handleImageSourceTypeChange('upload'),
                        className: 'form-radio h-4 w-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500 cursor-pointer'
                    }),
                    React.createElement('span', { className: 'ml-2' }, '🖼 Upload from Device')
                ),
                React.createElement('label', { htmlFor: 'sourceTypeUrl', className: 'flex items-center cursor-pointer text-sm text-gray-300' },
                    React.createElement('input', {
                        type: 'radio',
                        id: 'sourceTypeUrl',
                        name: 'imageSourceType',
                        value: 'url',
                        checked: imageSourceType === 'url',
                        onChange: () => handleImageSourceTypeChange('url'),
                        className: 'form-radio h-4 w-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500 cursor-pointer'
                    }),
                    React.createElement('span', { className: 'ml-2' }, '🌐 Import via URL')
                )
            ),
    
            // Conditional input based on imageSourceType
            imageSourceType === 'upload' && React.createElement('div', { className: 'flex flex-col gap-2 mt-1' },
                React.createElement('input', {
                    type: 'file',
                    id: 'customFaceFileInput',
                    ref: fileInputRef, // Assign ref
                    accept: '.jpg, .jpeg, .png',
                    onChange: handleFileUpload, // This is passed from App component
                    className: 'block w-full text-sm text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-purple-600 file:text-white hover:file:bg-purple-700 cursor-pointer focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900'
                }),
                React.createElement('p', {className: 'text-xs text-gray-500 mt-1'}, "Max 2MB. JPEG or PNG.")
            ),
    
            imageSourceType === 'url' && React.createElement('div', { className: 'flex items-center gap-2 mt-1' },
                React.createElement('input', {
                    type: 'text',
                    id: 'customFaceUrlInput',
                    placeholder: 'Paste image URL here... (e.g., https://example.com/face.jpg)',
                    value: tempUrl,
                    onChange: handleUrlInputChange,
                    onBlur: handleSetFaceImageFromUrl, // Validate and set on blur
                    className: 'flex-grow p-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 text-gray-100 text-sm'
                }),
                React.createElement('button', {
                    type: 'button',
                    onClick: handleSetFaceImageFromUrl,
                    className: 'px-3 py-2 text-xs bg-purple-600 hover:bg-purple-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500'
                }, 'Set URL')
            ),
    
            // Preview and Remove button (common for both sources)
            customFaceImageUrl && React.createElement('div', { className: 'mt-3 flex flex-col items-center gap-2 p-2 border border-gray-700 rounded-md bg-gray-700/30' },
                 React.createElement('p', { className: 'text-xs text-gray-400 self-start' }, 'Face Preview:'),
                React.createElement('img', { 
                    src: customFaceImageUrl, 
                    alt: 'Custom Face Preview', 
                    className: 'w-20 h-20 rounded-full object-cover border-2 border-purple-500 shadow-md',
                    onError: (e) => { 
                        e.target.style.display='none'; // Hide img tag on error
                        // Optionally, show a placeholder or error message next to it
                        if (!document.getElementById('img-error-msg')){
                            const errorP = document.createElement('p');
                            errorP.id = 'img-error-msg';
                            errorP.className = 'text-xs text-red-400';
                            errorP.textContent = 'Preview unavailable. Check URL or file.';
                            e.target.parentNode.appendChild(errorP);
                        }
                    },
                    onLoad: (e) => {
                         // Remove any previous error message on successful load
                        const errorMsgEl = document.getElementById('img-error-msg');
                        if (errorMsgEl) errorMsgEl.remove();
                        e.target.style.display=''; // Ensure img is visible
                    }
                }),
                React.createElement('button', {
                    type: 'button',
                    onClick: handleRemoveFaceImage,
                    className: 'mt-1 px-3 py-1 text-xs bg-red-600 hover:bg-red-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-red-500'
                }, 'Remove Image')
            )
        );
    };

    // Create Expression Picker
    const createMoodAndExpressionPicker = () => {
        if (!includePerson) return null;
    
        const expressionOptions = [
            { emoji: '🙂', label: 'Default', value: 'Default' },
            { emoji: '😊', label: 'Happy', value: 'Happy' },
            { emoji: '😳', label: 'Shocked', value: 'Shocked' },
            { emoji: '😍', label: 'Loved', value: 'Loved' },
            { emoji: '🤔', label: 'Thinking', value: 'Thinking' },
            { emoji: '😠', label: 'Angry', value: 'Angry' },
            { emoji: '😢', label: 'Crying', value: 'Crying' },
            { emoji: '😆', label: 'Laughing', value: 'Laughing' },
            { emoji: '😐', label: 'Neutral', value: 'Neutral' },
            { emoji: '😎', label: 'Proud', value: 'Proud' }
        ];
    
        const handleExpressionClick = (newValue) => {
            setSelectedExpression(newValue);
        };
    
        const handleExpressionKeyDown = (e, newValue) => {
            if (e.key === 'Enter' || e.key === ' ') {
                setSelectedExpression(newValue);
                e.preventDefault();
            }
        };
    
        const buttons = expressionOptions.map(option =>
            React.createElement('button', {
                key: option.value,
                type: 'button',
                onClick: () => handleExpressionClick(option.value),
                onKeyDown: (e) => handleExpressionKeyDown(e, option.value),
                title: option.label,
                className: `flex flex-col items-center justify-center p-2 rounded-lg border-2 text-center ${selectedExpression === option.value ? 'border-purple-500 bg-purple-700' : 'border-gray-600 bg-gray-700 hover:border-purple-400'} transition-all focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 w-full`,
                id: `expression-btn-${option.value.toLowerCase().replace(/\s+/g, '-')}`,
                'aria-label': `${option.emoji} ${option.label}`,
                'aria-pressed': selectedExpression === option.value
            },
                React.createElement('span', { 'aria-hidden': 'true', className: 'text-3xl' }, option.emoji),
                React.createElement('span', { className: 'block text-xs mt-1 text-gray-300' }, option.label)
            )
        );
    
        return React.createElement('div', { className: 'mood-expression-picker-section py-2', id: 'mood-expression-picker-section' },
            React.createElement('label', { className: 'mood-expression-picker-label block text-sm font-medium text-gray-300 mb-2' }, 'Mood & Expression:'),
            React.createElement('div', { className: 'mood-expression-picker-grid grid grid-cols-5 sm:grid-cols-5 gap-2' }, buttons)
        );
    };

    // Create Gender Selector
    const createGenderSelector = () => {
        if (!includePerson) return null;
        const genderOptions = [
            { emoji: '🤖', label: 'Auto', value: 'Auto' },
            { emoji: '👨', label: 'Male', value: 'Male' },
            { emoji: '👩', label: 'Female', value: 'Female' },
            { emoji: '🧑', label: 'Non-binary', value: 'Non-binary' }
        ];
        const tooltipText = "Specify the gender of the person in the thumbnail. 'Auto' lets the AI decide. This is only active if 'Include Person' is ON.";
    
        return React.createElement('div', { className: 'gender-selector-section py-2', id: 'gender-selector-section' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1'},
                React.createElement('label', { htmlFor: 'genderSelect', className: 'text-sm font-medium text-gray-300' }, 'Preferred Gender:'),
                createTooltipIcon(tooltipText, 'tooltip-gender')
            ),
            React.createElement('div', { className: 'grid grid-cols-4 gap-2 mt-2', role: 'radiogroup', 'aria-label': 'Gender selection' },
                genderOptions.map(gender => React.createElement('button', {
                    key: gender.value,
                    onClick: () => setSelectedGender(gender.value),
                    className: `gender-card flex flex-col items-center justify-center p-2 ${selectedGender === gender.value ? 'bg-purple-700 border-purple-500' : 'bg-gray-700 border-gray-600'} border rounded-md shadow-sm hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50`,
                    'aria-pressed': selectedGender === gender.value,
                    'aria-label': gender.label,
                    disabled: !includePerson
                },
                    React.createElement('span', { className: 'text-2xl mb-1' }, gender.emoji),
                    React.createElement('span', { className: 'text-xs font-medium' }, gender.label)
                ))
            )
        );
    };

    // Create text overlay text editor
    const createTextOverlayEditor = () => {
        if (!textOverlay) return null;
    
        const placeholder = 'YOUR VIDEO\nTITLE\nHERE';
        
        return React.createElement('div', { className: 'text-overlay-editor py-2' },
            React.createElement('div', { className: 'flex items-center justify-between mb-2' },
                React.createElement('label', { className: 'block text-sm font-medium text-gray-300' }, 'Overlay Text:'),
                React.createElement('button', {
                    type: 'button',
                    onClick: () => setIsEditingOverlayText(prev => !prev),
                    className: `px-2 py-1 text-xs rounded ${isEditingOverlayText ? 'bg-purple-700 text-white' : 'bg-gray-600 text-gray-300 hover:bg-purple-500'} transition-colors`,
                    'aria-label': isEditingOverlayText ? 'Close Text Editor' : 'Edit Overlay Text'
                }, isEditingOverlayText ? 'Close' : 'Edit')
            ),
            isEditingOverlayText && React.createElement('div', { className: 'mt-1' },
                React.createElement('textarea', {
                    id: 'overlayTextEditor',
                    className: 'w-full p-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm text-gray-100 resize-y focus:ring-purple-500 focus:border-purple-500',
                    rows: '3',
                    placeholder: placeholder,
                    value: overlayText,
                    onChange: (e) => setOverlayText(e.target.value),
                    'aria-label': 'Edit overlay text'
                }),
                React.createElement('p', { className: 'text-xs text-gray-400 mt-1' }, 'Pyramid shape recommended (wider at top, narrower at bottom)')
            ),
            !isEditingOverlayText && React.createElement('div', { className: 'mt-1 text-sm text-gray-300 overflow-hidden text-ellipsis h-16 bg-gray-700/50 p-2 rounded border border-gray-600' },
                (overlayText || placeholder).split('\n').map((line, i) => (
                    React.createElement('div', { key: i, className: 'text-center' }, line || ' ')
                ))
            )
        );
    };

    // Create text position selector
    const createTextPositionSelector = () => {
        if (!textOverlay) return null;
    
        const positions = [
            "Top Left", "Top Center", "Top Right",
            "Center",
            "Bottom Left", "Bottom Center", "Bottom Right"
        ];
    
        return React.createElement('div', { className: 'text-position-selector py-2' },
            React.createElement('label', { 
                htmlFor: 'textPositionSelect',
                className: 'block text-sm font-medium text-gray-300 mb-1'
            }, 'Text Position:'),
            React.createElement('select', {
                id: 'textPositionSelect',
                value: textPosition,
                onChange: (e) => setTextPosition(e.target.value),
                className: 'w-full p-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm text-gray-100 focus:ring-purple-500 focus:border-purple-500'
            }, positions.map(pos => React.createElement('option', { key: pos, value: pos }, pos)))
        );
    };

    // Create color palette and manual color pickers for text
    const createTextColorPickers = () => {
        if (!textOverlay) return null;
        
        // Tooltip text for color section
        const tooltipText = "Pick a single primary accent color. We'll automatically create a darker contrast for the gradient (secondary color).";

        // Helper to generate a darker secondary shade based on primary color
        const generateSecondaryColor = (hex, amount = 40) => {
            // Convert #RRGGBB to integer components
            let c = hex.startsWith('#') ? hex.substring(1) : hex;
            if (c.length === 3) {
                c = c.split('').map(x => x + x).join('');
            }
            let num = parseInt(c, 16);
            let r = (num >> 16) - amount;
            let g = ((num >> 8) & 0x00FF) - amount;
            let b = (num & 0x0000FF) - amount;
            r = Math.max(0, r);
            g = Math.max(0, g);
            b = Math.max(0, b);
            const toHex = (v) => v.toString(16).padStart(2, '0');
            return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
        };
        
        return React.createElement('div', { className: 'text-color-typography-section py-3 mt-1 border-t border-gray-700', id: 'text-color-typography-section' },
            // Section header with icon and tooltip
            React.createElement('div', { className: 'flex items-center justify-between mb-3' },
                React.createElement('div', { className: 'flex items-center gap-2' },
                    // Icon (using Solar Icons format like other sections)
                        React.createElement('span', { 
                        className: 'text-purple-400',
                        dangerouslySetInnerHTML: { __html: '<i class="solar solar-palette-bold-duotone"></i>' }
                    }),
                    React.createElement('h4', { className: 'text-sm font-semibold text-gray-200' }, 'Text Color & Typography')
                ),
                // Info tooltip
                createTooltipIcon(tooltipText, 'tooltip-text-color')
            ),
            
            // SINGLE Primary color picker
            React.createElement('div', { className: 'mb-3 flex flex-col gap-2' },
                React.createElement('label', { className: 'block text-xs font-medium text-gray-400' }, 'Primary Accent Color:'),
                React.createElement('div', { className: 'flex items-center gap-3' },
                    React.createElement('input', {
                        type: 'color',
                        id: 'primaryTextColor',
                        value: primaryTextColor,
                        onChange: (e) => {
                            const newPrimary = e.target.value;
                            const newSecondary = generateSecondaryColor(newPrimary);
                            setPrimaryTextColor(newPrimary);
                            setSecondaryTextColor(newSecondary);
                        },
                        className: 'w-10 h-10 p-0 border-0 bg-transparent cursor-pointer',
                        tabIndex: 0,
                        'aria-label': 'Primary text color picker'
                    }),
                    React.createElement('span', { className: 'font-mono text-xs text-gray-300' }, primaryTextColor.toUpperCase())
                ),
                React.createElement('p', { className: 'text-[10px] text-gray-500' }, 'Secondary shade generated automatically for gradient.')
            ),
            
            // NOTE: Secondary color picker removed – handled automatically.
 
            // Enhanced live preview
            React.createElement('div', { 
                className: 'rounded-md border border-gray-700 bg-gray-900 p-4 flex flex-col items-center mt-1 overflow-hidden',
                'aria-label': 'Text style preview'
            },
                // Preview header
                React.createElement('div', { className: 'w-full flex items-center justify-between mb-1' },
                    React.createElement('span', { className: 'text-xs text-gray-400 font-medium' }, 'Live Preview:'),
                    // Font & size indicator
                    React.createElement('span', { className: 'text-xs text-gray-500 font-mono' }, 
                        `${selectedFontFamily} / ${selectedTextSize}`
                    )
                ),
                React.createElement('div', {
                    className: 'w-full rounded overflow-hidden p-3 flex flex-col items-center justify-center text-center',
                    style: {
                        background: 'linear-gradient(135deg, #1a1a2e 0%, #222244 100%)',
                        minHeight: '80px'
                    }
                },
                    (overlayText || 'YOUR VIDEO TITLE')
                        .toUpperCase()
                        .split('\n')
                        .map((line, idx) =>
                            React.createElement('span', {
                                key: idx,
                                style: {
                                    fontFamily: selectedFontFamily,
                                    fontSize: selectedTextSize === 'Large' ? '1.8rem' : selectedTextSize === 'Medium' ? '1.4rem' : '1rem',
                                    fontWeight: 'bold',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.02em',
                                    lineHeight: '1.1',
                                    marginBottom: '0.2em',
                                    background: `linear-gradient(90deg, ${primaryTextColor}, ${secondaryTextColor})`,
                                    WebkitBackgroundClip: 'text',
                                    WebkitTextFillColor: 'transparent',
                                    backgroundClip: 'text',
                                    color: 'transparent',
                                },
                                // Fallback for browsers that don't support background-clip:text
                                className: 'gradient-text',
                            }, line)
                        )
                )
            )
        );
    };

    // Group sections based on functionality
    const basicControlsSection = React.createElement('div', {className: 'flex flex-col gap-2'},
        createToggle('Enable Text Overlay?', 'toggleText', textOverlay, setTextOverlay, 'Show bold, eye-catching text on your thumbnail. Recommended for most YouTube videos.'),
        createToggle('Include Person?', 'togglePerson', includePerson, setIncludePerson, 'Add a human figure or face to your thumbnail. Faces increase engagement and click-through rates.'),
        createToggle('Include Icons?', 'toggleIcons', includeIcons, setIncludeIcons, 'Add relevant icons, badges, or symbols to visually reinforce your video topic.')
    );

    const textSettingsSection = React.createElement('div', {className: 'flex flex-col gap-2'},
        createTextOverlayEditor(),
        createTextPositionSelector(),
        createTextSizeSelector(),
        // createFontSelector(), // HIDE FONT SELECTOR FOR STAGE VERSION
        createTextColorPickers()
    );

    const personSettingsSection = React.createElement('div', {className: 'flex flex-col gap-2'},
        createMoodAndExpressionPicker(),
        createGenderSelector(),
        createFaceUploadSection()
    );

    // Render the panel with collapsible sections
    return (
        React.createElement('div', { className: 'p-2 bg-gray-800 rounded-lg flex flex-col gap-3 overflow-y-auto max-h-[calc(100vh-10rem)] min-h-0' },
            // Sticky heading with shadow for clarity (removed border divider)
            React.createElement('h2', {
                className: 'text-lg font-semibold text-purple-400 mb-2 sticky top-0 bg-gray-800 py-2 z-20',
                // style: { boxShadow: '0 2px 8px 0 rgba(0,0,0,0.10)' } // fallback for shadow
            }, 'Design Controls'),
            // NOTE: Sticky context relies on this container being the scrollable parent (overflow-y-auto, max-h-[]). If you change layout, check sticky behavior!
            
            // Basic Controls Section (expanded by default)
            React.createElement(CollapsibleSection, {
                title: 'Basic Controls',
                icon: 'solar:magic-stick-3-bold',
                defaultExpanded: true,
                id: 'basic-controls'
            }, basicControlsSection),
            
            // Text Settings Section (conditionally visible, expanded by default if text overlay is enabled)
            textOverlay && React.createElement(CollapsibleSection, {
                title: 'Text Settings',
                icon: 'solar:text-bold-duotone',
                defaultExpanded: true,
                id: 'text-settings'
            }, textSettingsSection),
            
            // Person Settings Section (conditionally visible, expanded by default if include person is enabled)
            includePerson && React.createElement(CollapsibleSection, {
                title: 'Person Settings',
                icon: 'solar:user-bold-duotone',
                defaultExpanded: true,
                id: 'person-settings'
            }, personSettingsSection),
            
            // Background Controls Section (collapsed by default)
            React.createElement(CollapsibleSection, {
                title: 'Background Styles',
                icon: 'solar:gallery-bold-duotone',
                defaultExpanded: false,
                id: 'background-controls'
            }, 
                React.createElement(BackgroundControls, {
                        selectedBackgroundType,
                        selectedBackgroundStyleId,
                        handleBackgroundStyleSelect,
                        selectedSolidBgColor,
                        handleSolidBgColorChange
                    })
            ),
            
            /* Advanced Settings section removed for MVP */
        )
    );
};

// Export the component for dynamic import
export default ControlPanel; 