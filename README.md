# Yhumbnail-spark-New-version
MVP YouTube Thumbnail Generator built with <PERSON><PERSON>, <PERSON><PERSON><PERSON> (Hero UI), and Heroicons. Hackathon-ready, private repo.

---

## 📁 Project Structure

```
/ (root)
├── public/                # Static assets (backgrounds, icons, etc.)
├── Templates/             # Premade template categories
├── prompts/               # Prompt logic, rules, and enhancement files
├── src/                   # Main React app source code
│   ├── components/        # All React components (UI, admin, background, etc.)
│   ├── pages/             # Page-level components (Welcome, Admin, etc.)
│   ├── styles/            # CSS files (Tailwind, custom, admin, etc.)
│   ├── utils/             # Utility JS files (prompt logic, config, etc.)
│   └── App.jsx            # Main app entry point
├── package.json           # Project dependencies and scripts
├── config.js              # App configuration
├── README.md              # This file
└── ...                    # Other config and documentation files
```

---

## 🚀 Installation & Usage

### 1. **Bolt.new (One-Click Deploy)**

1. Go to [https://bolt.new](https://bolt.new)
2. Click **"Import from GitHub"**
3. Paste your repo URL: `https://github.com/madhoundes/Yhumbnail-spark-New-version`
4. Bolt will auto-detect and install dependencies
5. Click **"Run"** — your app will be live at a Bolt URL!

**No extra configuration needed.**

---

### 2. **Local Development**

#### **Requirements:**
- Node.js (v18+ recommended)
- npm (v9+ recommended)

#### **Steps:**

```bash
# Clone the repo
$ git clone https://github.com/madhoundes/Yhumbnail-spark-New-version.git
$ cd Yhumbnail-spark-New-version

# Install dependencies
$ npm install

# Start the development server
$ npm start

# The app will open at http://localhost:3000 (or next available port)
```

---

## 🛠️ Tech Stack
- React (functional components)
- Tailwind CSS via Hero UI CDN (dark mode enabled)
- Heroicons & Solar Icons (CDN)
- Vite (for fast local dev)
- No backend required for MVP (mock data, localStorage)

---

## 📦 Features
- Cinematic YouTube thumbnail generator
- Premade template categories
- Prompt enhancement logic
- Responsive, mobile-first UI
- User profile dashboard (see `/src/components/profile/`)
- Admin dashboard (see `/src/components/admin/`)
- All code and assets version-controlled

---

## 📚 Documentation
- See `/prompts/` for all prompt logic and enhancement rules
- See `/src/components/` for UI and feature components
- See `profile-implementation-guide.md` for user profile dashboard setup
- See `cursor-rules-user-profile.mdc` for MCP rules

---

## 🧑‍💻 Contributing
- Fork, branch, and PR as usual
- Please follow the code style and structure in `cursor-rules-user-profile.mdc`

---

## 📝 License
This project is private for hackathon/demo use. Contact @madhoundes for access or licensing.