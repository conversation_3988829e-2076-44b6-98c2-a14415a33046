// Import React from the installed packages
const React = window.React;
const { useEffect, useState, useRef, Fragment } = React;
const ReactDOM = window.ReactDOM;

// Import CSS files
import './styles/layout.css'; // Import the new three-panel layout styles
import './styles/background-controls.css'; // Import background controls CSS
import './styles/prompt.css'; // Import prompt component CSS
import './styles/collapsible-sections.css'; // Import styles for collapsible sections
import './styles/global-tooltips.css'; // Import global tooltips CSS
import CollapsibleControlPanel from './components/ControlPanel.jsx'; // NEW: external collapsible sidebar component
import { CollapsibleSection } from './components/ui/CollapsibleSection.jsx';

// Helper function to create an info icon using Solar Icon Set via Iconify
const createInfoIcon = (className = 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help') => {
    return React.createElement('span', {
        className: className,
        role: 'img',
        'aria-label': 'Information',
        dangerouslySetInnerHTML: {
            __html: `<span class="iconify" data-icon="solar:info-circle-linear" style="color: currentColor;"></span>`
        }
    });
};

// ================= Configuration and Constants =================

// ... existing code ...

// Import API key
import { OPENAI_API_KEY } from '/config.js';
import { buildPrompt } from './utils/promptFormatter.js'; // UPDATED: Correct relative path
import { findPlaceholders } from './utils/textUtils.js'; // Import findPlaceholders from utility file
import { enhancePrompt } from './utils/promptEnhancer.js'; // Fallback enhancer (regex based)
import { getSmartTitle } from './utils/openaiPromptEnhancer.js'; // NEW: OpenAI powered enhancer
import { suggestedPrompts, startTypewriter, prefersReducedMotion, startImproveTypewriter } from './utils/typewriterSuggestions.js'; // NEW: Typewriter animation
import { findBackgroundStyleById, BACKGROUND_CATEGORIES as TEMPLATE_BG_CATEGORIES } from './utils/backgroundConfig.js'; // NEW: Import for background utility
import { BackgroundTemplateModal } from './components/background/BackgroundTemplateModal.jsx';
// ADD IMPORT FOR ADMIN DASHBOARD
import { AdminDashboard } from './pages/admin/AdminDashboard.jsx';

// IMPORT TEMPLATE ARRAYS
import moviesTemplates from '../Templates/Movies.js';
import techTemplates from '../Templates/Tech.js';
import gamingTemplates from '../Templates/Gaming.js';
import vloggingTemplates from '../Templates/Vlogging.js';
import reactionTemplates from '../Templates/Reaction.js';

// ================= PREMADE TEMPLATES DATA =================
const premadeTemplatesData = [
    {
        id: "travel-vlog",
        name: "Travel & Vlog",
        categoryImagePlaceholder: { text: "Travel & Vlog", bgColor: "bg-orange-600" },
        unsplashImage: "https://images.unsplash.com/photo-1488646953014-85cb44e25828?auto=format&fit=crop&w=800&q=80",
        templates: vloggingTemplates
    },
    {
        id: "health-fitness",
        name: "Health Fitness",
        categoryImagePlaceholder: { text: "Health Fitness", bgColor: "bg-green-600" },
        unsplashImage: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "fitness-transformation",
                name: "30-Day [WORKOUT] Transformation",
                description: "For fitness challenges and workout content.",
                promptBase: "Design a motivational YouTube thumbnail for '30-Day [WORKOUT] Transformation'. Show a fit person in workout gear with before/after elements. Text overlay: '30-DAY TRANSFORMATION'. Use energetic colors like green and orange.",
                settingsToApply: { 
                    includePerson: true, 
                    selectedExpression: 'Confident', 
                    textOverlay: true, 
                    userPromptFocus: "30-Day [WORKOUT] Transformation",
                    overlayText: "30-DAY\n[WORKOUT]\nTRANSFORMATION"
                },
                templateImagePlaceholder: { text: "Transformation", bgColor: "bg-green-500" }
            }
        ]
    },
    {
        id: "business",
        name: "Business",
        categoryImagePlaceholder: { text: "Business", bgColor: "bg-blue-600" },
        unsplashImage: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "business-success",
                name: "How I Made $[AMOUNT] in [TIME]",
                description: "For business success and entrepreneurship content.",
                promptBase: "Create a professional YouTube thumbnail for 'How I Made $[AMOUNT] in [TIME]'. Show a confident entrepreneur with money/success symbols. Text overlay: 'MADE $[AMOUNT]'. Use professional blue and gold colors.",
                settingsToApply: { 
                    includePerson: true, 
                    selectedExpression: 'Confident', 
                    textOverlay: true, 
                    userPromptFocus: "How I Made $[AMOUNT] in [TIME]",
                    overlayText: "MADE\n$[AMOUNT]\nIN [TIME]"
                },
                templateImagePlaceholder: { text: "Business Success", bgColor: "bg-blue-500" }
            }
        ]
    },
    {
        id: "art-tech",
        name: "Art & Tech",
        categoryImagePlaceholder: { text: "Art & Tech", bgColor: "bg-purple-600" },
        unsplashImage: "https://images.unsplash.com/photo-**********-9bc0b252726f?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "tech-review",
                name: "[GADGET] Review: Game Changer?",
                description: "For tech reviews and digital art content.",
                promptBase: "Generate a modern YouTube thumbnail for '[GADGET] Review: Game Changer?'. Show the gadget prominently with tech elements and a reviewer. Text overlay: 'GAME CHANGER?'. Use sleek purple and blue tech colors.",
                settingsToApply: { 
                    includePerson: true, 
                    selectedExpression: 'Thinking', 
                    textOverlay: true, 
                    userPromptFocus: "[GADGET] Review: Game Changer?",
                    overlayText: "[GADGET]\nGAME\nCHANGER?"
                },
                templateImagePlaceholder: { text: "Tech Review", bgColor: "bg-purple-500" }
            }
        ]
    },
    {
        id: "movies",
        name: "Movies",
        categoryImagePlaceholder: { text: "Movies", bgColor: "bg-red-600" },
        unsplashImage: "https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=800&q=80",
        templates: moviesTemplates
    },
    {
        id: "tech",
        name: "Tech",
        categoryImagePlaceholder: { text: "Tech", bgColor: "bg-blue-500" },
        unsplashImage: "https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80",
        templates: techTemplates
    },
    {
        id: "reactions",
        name: "Reactions",
        categoryImagePlaceholder: { text: "Reactions", bgColor: "bg-pink-500" },
        unsplashImage: "https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=800&q=80",
        templates: reactionTemplates
    },
    {
        id: "gaming",
        name: "Gaming",
        categoryImagePlaceholder: { text: "Gaming", bgColor: "bg-green-500" },
        unsplashImage: "https://images.unsplash.com/photo-1542751371-adc38448a05e?auto=format&fit=crop&w=800&q=80",
        templates: gamingTemplates
    }
];

// ================= EXPANDED TEMPLATE CATEGORIES =================
// These categories will be shown in the "Show More" modal
const expandedTemplatesData = [
    {
        id: "business",
        name: "Business",
        description: "Professional, persuasive templates for business content",
        categoryImagePlaceholder: { text: "Business", bgColor: "bg-blue-800" },
        unsplashImage: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "business-pitch-deck",
                name: "Pitch Deck Secrets",
                description: "Eye-catching slide for startup pitches.",
                promptBase: "Create a professional YouTube thumbnail for 'Pitch Deck Secrets'. Show a confident presenter with a slick visual presentation in the background. Text overlay: 'PITCH DECK SECRETS'. Use a modern corporate style with blue and gray color scheme.",
                settingsToApply: { 
                    includePerson: true, 
                    selectedExpression: 'Confident', 
                    textOverlay: true, 
                    userPromptFocus: "Pitch Deck Secrets",
                    overlayText: "PITCH DECK\nSECRETS"
                },
                templateImagePlaceholder: { text: "Pitch Deck", bgColor: "bg-blue-800" }
            },
            {
                id: "business-market-trends",
                name: "Market Trends 2024",
                description: "Data-driven, modern analytics look.",
                promptBase: "Generate a YouTube thumbnail for 'Market Trends 2024'. Feature data visualizations, charts, or graphs with an upward trend. Text overlay: 'MARKET TRENDS 2024'. Use a modern, professional style with a blue gradient background.",
                settingsToApply: { 
                    includePerson: false, 
                    includeIcons: true,
                    textOverlay: true, 
                    userPromptFocus: "Market Trends 2024",
                    overlayText: "MARKET\nTRENDS 2024"
                },
                templateImagePlaceholder: { text: "Market Trends", bgColor: "bg-blue-800" }
            },
            {
                id: "business-remote-team",
                name: "Remote Team Success",
                description: "Friendly, collaborative team vibe.",
                promptBase: "Design a YouTube thumbnail for 'Remote Team Success'. Show a diverse team in a virtual meeting, looking productive and happy. Text overlay: 'REMOTE TEAM SUCCESS'. Use a friendly, collaborative style with warm colors.",
                settingsToApply: { 
                    includePerson: true, 
                    selectedExpression: 'Happy', 
                    textOverlay: true, 
                    userPromptFocus: "Remote Team Success",
                    overlayText: "REMOTE TEAM\nSUCCESS"
                },
                templateImagePlaceholder: { text: "Remote Team", bgColor: "bg-blue-800" }
            }
        ]
    },
    {
        id: "health-fitness",
        name: "Health & Fitness",
        description: "Energetic, motivational templates for health and fitness content",
        categoryImagePlaceholder: { text: "Fitness", bgColor: "bg-green-800" },
        unsplashImage: "https://images.unsplash.com/photo-1517836357463-d25dfeac3438?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "fitness-30day-challenge",
                name: "30-Day Challenge",
                description: "Motivational, energetic fitness challenge.",
                promptBase: "Create an energetic YouTube thumbnail for a '30-Day Challenge'. Show a fit person in dynamic exercise pose with visible results. Text overlay: '30-DAY CHALLENGE'. Use bright, motivational colors with high contrast.",
                settingsToApply: { 
                    includePerson: true, 
                    selectedExpression: 'Determined', 
                    textOverlay: true, 
                    userPromptFocus: "30-Day Challenge",
                    overlayText: "30-DAY\nCHALLENGE"
                },
                templateImagePlaceholder: { text: "30-Day Challenge", bgColor: "bg-green-800" }
            },
            {
                id: "fitness-meal-prep",
                name: "Meal Prep Hacks",
                description: "Clean, food-focused healthy eating.",
                promptBase: "Design a YouTube thumbnail for 'Meal Prep Hacks'. Feature organized, colorful meal prep containers with healthy food. Text overlay: 'MEAL PREP HACKS'. Use a clean, bright style with green and white color scheme.",
                settingsToApply: { 
                    includePerson: false, 
                    includeIcons: true,
                    textOverlay: true, 
                    userPromptFocus: "Meal Prep Hacks",
                    overlayText: "MEAL PREP\nHACKS"
                },
                templateImagePlaceholder: { text: "Meal Prep", bgColor: "bg-green-800" }
            },
            {
                id: "fitness-hiit-workout",
                name: "HIIT Workout",
                description: "Dynamic, action-packed workout shot.",
                promptBase: "Generate a YouTube thumbnail for a 'HIIT Workout'. Show an athletic person mid-workout with high energy and intensity. Text overlay: 'HIIT WORKOUT'. Use dynamic, intense style with red and black color scheme.",
                settingsToApply: { 
                    includePerson: true, 
                    selectedExpression: 'Intense', 
                    textOverlay: true, 
                    userPromptFocus: "HIIT Workout",
                    overlayText: "HIIT\nWORKOUT"
                },
                templateImagePlaceholder: { text: "HIIT Workout", bgColor: "bg-green-800" }
            }
        ]
    },
    {
        id: "travel",
        name: "Travel",
        description: "Scenic, adventurous templates for travel content",
        categoryImagePlaceholder: { text: "Travel", bgColor: "bg-yellow-800" },
        unsplashImage: "https://images.unsplash.com/photo-1503220317375-aaad61436b1b?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "travel-hidden-gems",
                name: "Hidden Gems",
                description: "Scenic, off-the-beaten-path locations.",
                promptBase: "Create a stunning YouTube thumbnail for 'Hidden Gems'. Show a breathtaking landscape or secret spot that looks magical and undiscovered. Text overlay: 'HIDDEN GEMS'. Use a dreamy, cinematic style with vibrant colors.",
                settingsToApply: { 
                    includePerson: false, 
                    textOverlay: true, 
                    userPromptFocus: "Hidden Gems - Travel Secrets",
                    overlayText: "HIDDEN\nGEMS"
                },
                templateImagePlaceholder: { text: "Hidden Gems", bgColor: "bg-yellow-800" }
            },
            {
                id: "travel-packing-list",
                name: "Ultimate Packing List",
                description: "Organized, checklist style for travelers.",
                promptBase: "Design a YouTube thumbnail for 'Ultimate Packing List'. Show neatly organized travel items or suitcase with essentials. Text overlay: 'ULTIMATE PACKING LIST'. Use a clean, organized style with travel-themed colors.",
                settingsToApply: { 
                    includePerson: false, 
                    includeIcons: true,
                    textOverlay: true, 
                    userPromptFocus: "Ultimate Packing List",
                    overlayText: "ULTIMATE\nPACKING LIST"
                },
                templateImagePlaceholder: { text: "Packing List", bgColor: "bg-yellow-800" }
            },
            {
                id: "travel-city-vs-nature",
                name: "City vs. Nature",
                description: "Split-screen comparison for travelers.",
                promptBase: "Generate a YouTube thumbnail for 'City vs. Nature'. Create a split-screen effect showing urban skyline on one side and natural landscape on the other. Text overlay: 'CITY vs. NATURE'. Use a dramatic, contrasting style.",
                settingsToApply: { 
                    includePerson: false, 
                    textOverlay: true, 
                    userPromptFocus: "City vs. Nature - Travel Comparison",
                    overlayText: "CITY vs.\nNATURE"
                },
                templateImagePlaceholder: { text: "City vs Nature", bgColor: "bg-yellow-800" }
            }
        ]
    },
    {
        id: "finance",
        name: "Finance",
        description: "Trustworthy, informative templates for financial content",
        categoryImagePlaceholder: { text: "Finance", bgColor: "bg-green-700" },
        unsplashImage: "https://images.unsplash.com/photo-1567427017947-545c5f8d16ad?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "finance-saved-10k",
                name: "How I Saved $10,000",
                description: "Relatable, personal finance success.",
                promptBase: "Create a YouTube thumbnail for 'How I Saved $10,000'. Show a relatable person looking happy with money/savings visual cues. Text overlay: 'HOW I SAVED $10,000'. Use a trustworthy, positive style with green accents.",
                settingsToApply: { 
                    includePerson: true, 
                    selectedExpression: 'Happy', 
                    textOverlay: true, 
                    userPromptFocus: "How I Saved $10,000",
                    overlayText: "HOW I SAVED\n$10,000"
                },
                templateImagePlaceholder: { text: "Saved $10k", bgColor: "bg-green-700" }
            },
            {
                id: "finance-crypto-explained",
                name: "Crypto Explained",
                description: "Futuristic, techy cryptocurrency guide.",
                promptBase: "Design a YouTube thumbnail for 'Crypto Explained'. Feature crypto symbols or blockchain visualization with a futuristic tech aesthetic. Text overlay: 'CRYPTO EXPLAINED'. Use a modern, tech-focused style with blue lighting.",
                settingsToApply: { 
                    includePerson: false, 
                    includeIcons: true,
                    textOverlay: true, 
                    userPromptFocus: "Crypto Explained",
                    overlayText: "CRYPTO\nEXPLAINED"
                },
                templateImagePlaceholder: { text: "Crypto", bgColor: "bg-green-700" }
            },
            {
                id: "finance-budgeting-101",
                name: "Budgeting 101",
                description: "Simple, approachable financial basics.",
                promptBase: "Generate a YouTube thumbnail for 'Budgeting 101'. Show simple budget visuals or money-saving concepts in an approachable way. Text overlay: 'BUDGETING 101'. Use a clean, beginner-friendly style with calming colors.",
                settingsToApply: { 
                    includePerson: false, 
                    includeIcons: true,
                    textOverlay: true, 
                    userPromptFocus: "Budgeting 101",
                    overlayText: "BUDGETING\n101"
                },
                templateImagePlaceholder: { text: "Budgeting 101", bgColor: "bg-green-700" }
            }
        ]
    },
    {
        id: "art-design",
        name: "Art & Design",
        description: "Creative, artistic templates for design content",
        categoryImagePlaceholder: { text: "Art & Design", bgColor: "bg-purple-800" },
        unsplashImage: "https://images.unsplash.com/photo-1513364776144-60967b0f800f?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "design-speedpaint",
                name: "Speedpaint Timelapse",
                description: "Colorful, creative art process.",
                promptBase: "Create a YouTube thumbnail for 'Speedpaint Timelapse'. Show vibrant artwork in progress with art supplies visible. Text overlay: 'SPEEDPAINT TIMELAPSE'. Use a colorful, artistic style with paint splatter effects.",
                settingsToApply: { 
                    includePerson: false, 
                    textOverlay: true, 
                    userPromptFocus: "Speedpaint Timelapse",
                    overlayText: "SPEEDPAINT\nTIMELAPSE"
                },
                templateImagePlaceholder: { text: "Speedpaint", bgColor: "bg-purple-800" }
            },
            {
                id: "design-logo-tips",
                name: "Logo Design Tips",
                description: "Minimal, professional design advice.",
                promptBase: "Design a YouTube thumbnail for 'Logo Design Tips'. Feature clean, minimal logo design examples or process sketch. Text overlay: 'LOGO DESIGN TIPS'. Use a professional, designer-focused style with grid background.",
                settingsToApply: { 
                    includePerson: false, 
                    includeIcons: true,
                    textOverlay: true, 
                    userPromptFocus: "Logo Design Tips",
                    overlayText: "LOGO DESIGN\nTIPS"
                },
                templateImagePlaceholder: { text: "Logo Design", bgColor: "bg-purple-800" }
            },
            {
                id: "design-before-after",
                name: "Before & After",
                description: "Transformation, side-by-side comparison.",
                promptBase: "Generate a YouTube thumbnail for 'Before & After'. Create a split screen showing dramatic before/after transformation of a design project. Text overlay: 'BEFORE & AFTER'. Use a high-contrast, impactful style highlighting the difference.",
                settingsToApply: { 
                    includePerson: false, 
                    textOverlay: true, 
                    userPromptFocus: "Before & After Design Transformation",
                    overlayText: "BEFORE\n& AFTER"
                },
                templateImagePlaceholder: { text: "Before & After", bgColor: "bg-purple-800" }
            }
        ]
    }
];

// ================= Constants =================
// Color palette presets for text overlays
const TEXT_COLOR_PALETTES = [
    { id: 'default', name: 'Default', colors: ['#FFFFFF', '#FFFF00'], category: 'basic' },
    { id: 'glacier', name: 'Glacier', colors: ['#7BDFF2', '#B2F7EF', '#EFFFE9'], category: 'cold' },
    { id: 'blueberry', name: 'Blueberry', colors: ['#1F8A70', '#004358', '#BEDB39'], category: 'cold' },
    { id: 'minty', name: 'Minty', colors: ['#A9FBD7', '#CBF1F5', '#D6FFF6'], category: 'cold' },
    { id: 'coldspring', name: 'Cold Spring', colors: ['#A3F7BF', '#5EDFFF', '#B967FF'], category: 'cold' },
    { id: 'deepsea', name: 'Deep Sea', colors: ['#1D1E2C', '#3D426B', '#8797AF', '#C3CBDC'], category: 'cold' },
    { id: 'fiery', name: 'Fiery', colors: ['#FF5E5B', '#FFED66', '#FFA45B'], category: 'warm' },
    { id: 'sunset', name: 'Sunset', colors: ['#FF9F1C', '#FFBF69', '#FFF7F8'], category: 'warm' },
    { id: 'neon', name: 'Neon', colors: ['#F72585', '#4CC9F0', '#4361EE'], category: 'gaming' },
    { id: 'corporate', name: 'Corporate', colors: ['#003049', '#FCBF49', '#EAE2B7'], category: 'professional' }
];

const BACKGROUND_STYLES_DATA = [
    // Solid Colors
    { id: 'solid-red', name: 'Red', category: 'solid', previewImageUrl: '/assets/background-previews/solid/red.png', type: 'solid', color: '#FF0000' },
    { id: 'solid-blue', name: 'Blue', category: 'solid', previewImageUrl: '/assets/background-previews/solid/blue.png', type: 'solid', color: '#0000FF' },
    // Mesh Gradients
    { id: 'mesh-vaporwave', name: 'Vaporwave', category: 'mesh', previewImageUrl: '/assets/background-previews/mesh/vaporwave.png', type: 'template', template_id: 'mesh-vaporwave' },
    { id: 'mesh-warm-burst', name: 'Warm Burst', category: 'mesh', previewImageUrl: '/assets/background-previews/mesh/warm-burst.png', type: 'template', template_id: 'mesh-warm-burst' },
    // Sunburst / Halftone (Initial Category)
    { id: 'sunburst-yellow', name: 'Sunburst Yellow', category: 'sunburst', previewImageUrl: '/assets/background-previews/sunburst/halftone-yellow.png', type: 'template', template_id: 'sunburst-yellow' },
    { id: 'comic-orange', name: 'Comic Orange', category: 'sunburst', previewImageUrl: '/assets/background-previews/sunburst/comic-orange.png', type: 'template', template_id: 'comic-orange' },
    // Cinematic Vignette
    { id: 'cinematic-vignette', name: 'Soft Vignette', category: 'cinematic', previewImageUrl: '/assets/background-previews/cinematic/vignette-soft.png', type: 'template', template_id: 'cinematic-vignette' },
    { id: 'cinematic-glow', name: 'Tech Glow', category: 'cinematic', previewImageUrl: '/assets/background-previews/cinematic/glow-tech.png', type: 'template', template_id: 'cinematic-glow' },
    // Thematic Templates
    { id: 'thematic-sci-fi', name: 'Sci-Fi Grid', category: 'thematic', previewImageUrl: '/assets/background-previews/thematic/sci-fi-grid.png', type: 'template', template_id: 'thematic-sci-fi' },
    { id: 'thematic-cozy', name: 'Cozy Desk', category: 'thematic', previewImageUrl: '/assets/background-previews/thematic/learning-cozy.png', type: 'template', template_id: 'thematic-cozy' },
    // Halftone / Comic (New Category)
    { id: 'halftone-retro', name: 'Retro Comic', category: 'halftone', previewImageUrl: '/assets/background-previews/halftone/retro-comic.png', type: 'template', template_id: 'halftone-retro' },
    { id: 'halftone-dotburst', name: 'Dot Burst', category: 'halftone', previewImageUrl: '/assets/background-previews/halftone/dotburst.png', type: 'template', template_id: 'halftone-dotburst' },
    // Neon / Glow
    { id: 'neon-purple', name: 'Purple Glow', category: 'neon', previewImageUrl: '/assets/background-previews/neon/neon-purple-glow.png', type: 'template', template_id: 'neon-purple' },
    { id: 'neon-blue', name: 'Electric Blue', category: 'neon', previewImageUrl: '/assets/background-previews/neon/electric-blue.png', type: 'template', template_id: 'neon-blue' },
    // Bokeh / Light Leak
    { id: 'bokeh-orange', name: 'Soft Orange Bokeh', category: 'bokeh', previewImageUrl: '/assets/background-previews/bokeh/soft-orange.png', type: 'template', template_id: 'bokeh-orange' },
    { id: 'bokeh-leak', name: 'Camera Leak', category: 'bokeh', previewImageUrl: '/assets/background-previews/bokeh/camera-leak.png', type: 'template', template_id: 'bokeh-leak' },
    // Doodle / Handdrawn
    { id: 'doodle-arrows', name: 'Doodle Arrows', category: 'doodle', previewImageUrl: '/assets/background-previews/doodle/arrows.png', type: 'template', template_id: 'doodle-arrows' },
    { id: 'doodle-whiteboard', name: 'Whiteboard', category: 'doodle', previewImageUrl: '/assets/background-previews/doodle/whiteboard.png', type: 'template', template_id: 'doodle-whiteboard' },
    // Pattern / Texture
    { id: 'pattern-grid', name: 'Light Grid', category: 'pattern', previewImageUrl: '/assets/background-previews/pattern/grid-light.png', type: 'template', template_id: 'pattern-grid' },
    { id: 'pattern-noise', name: 'Paper Noise', category: 'pattern', previewImageUrl: '/assets/background-previews/pattern/paper-noise.png', type: 'template', template_id: 'pattern-noise' },
    // Image Upload Option
    { id: 'upload-image', name: 'Upload Image', category: 'upload', previewImageUrl: '', type: 'upload' }
];

const BACKGROUND_CATEGORIES = [
    { id: 'solid', name: 'Solid Color', icon: '🎨' },
    { id: 'mesh', name: 'Mesh Gradient', icon: '🌈' },
    { id: 'sunburst', name: 'Sunburst', icon: '☀️' },
    { id: 'cinematic', name: 'Cinematic', icon: '🎬' },
    { id: 'thematic', name: 'Thematic', icon: '🧠' },
    { id: 'halftone', name: 'Halftone/Comic', icon: '🟡' },
    { id: 'neon', name: 'Neon/Glow', icon: '✨' },
    { id: 'bokeh', name: 'Bokeh/Leak', icon: '🔆' },
    { id: 'doodle', name: 'Doodle', icon: '✏️' },
    { id: 'pattern', name: 'Pattern/Texture', icon: '🟫' },
    { id: 'upload', name: 'Upload Image', icon: '🖼️' },
];

// Cinematic style suffix applied to all premade templates
const CINEMATIC_SUFFIX = " Use a cinematic composition with dynamic camera angles, rich layered textures, vibrant high-contrast colours, dramatic lighting (deep shadows + glowing highlights), and engaging props/icons that emphasise the topic.";

// ================= Inline Components (avoid dynamic import issues) =================

const PromptInput = ({ displayValue, onChange, isLocked, onImprovePrompt, isTyping, cursorVisible }) => {
    // Add React hooks for typewriter animation
    const [placeholderText, setPlaceholderText] = React.useState('e.g., A surprised cat reacting to a cucumber, dramatic lighting');
    const typewriterRef = React.useRef(null);
    const inputRef = React.useRef(null);
    const [isTypewriterActive, setIsTypewriterActive] = React.useState(false);
    
    // Initialize typewriter animation on component mount if user prefers animations
    React.useEffect(() => {
        // Don't start animation if:
        // - User already has text
        // - Input is locked or already typing
        // - User prefers reduced motion
        if (displayValue || isLocked || isTyping || prefersReducedMotion()) {
            return;
        }
        
        // Add keyboard shortcut to toggle typewriter sounds (Ctrl/Cmd + Shift + S)
        const handleKeyDown = (e) => {
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'S') {
                e.preventDefault();
                const currentSetting = localStorage.getItem('typewriter-sounds') === 'true';
                const newSetting = !currentSetting;
                localStorage.setItem('typewriter-sounds', newSetting.toString());
                
                // Show a brief notification
                const notification = document.createElement('div');
                notification.textContent = `Typewriter sounds ${newSetting ? 'enabled' : 'disabled'}`;
                notification.style.cssText = `
                    position: fixed; top: 20px; right: 20px; z-index: 9999;
                    background: rgba(139, 92, 246, 0.9); color: white;
                    padding: 12px 20px; border-radius: 8px; font-size: 14px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    transition: opacity 0.3s ease-in-out;
                `;
                document.body.appendChild(notification);
                setTimeout(() => {
                    notification.style.opacity = '0';
                    setTimeout(() => document.body.removeChild(notification), 300);
                }, 2000);
                
                // Restart typewriter with new setting if active
                if (typewriterRef.current && isTypewriterActive) {
                    typewriterRef.current.restart();
                }
            }
        };
        
        document.addEventListener('keydown', handleKeyDown);
        
        // Short delay before starting animation
        const timeoutId = setTimeout(() => {
            if (inputRef.current) {
                setIsTypewriterActive(true);
                // Enable sound effects on user interaction (optional)
                // Tip: Press Ctrl/Cmd + Shift + S to toggle typewriter sounds
                const enableSounds = localStorage.getItem('typewriter-sounds') === 'true';
                typewriterRef.current = startTypewriter(
                    inputRef.current,
                    suggestedPrompts,
                    setPlaceholderText,
                    enableSounds
                );
            }
        }, 800); // Delay start to let page load first
        
        return () => {
            // Cleanup on unmount
            clearTimeout(timeoutId);
            document.removeEventListener('keydown', handleKeyDown);
            if (typewriterRef.current) {
                typewriterRef.current.stop();
            }
            setIsTypewriterActive(false);
        };
    }, []);
    
    // Stop animation when user types or when input changes
    React.useEffect(() => {
        if (displayValue && typewriterRef.current) {
            typewriterRef.current.stop();
            setIsTypewriterActive(false);
            // Reset to default placeholder
            setPlaceholderText('e.g., A surprised cat reacting to a cucumber, dramatic lighting');
        }
    }, [displayValue]);
    
    // Stop animation when input is locked or typing
    React.useEffect(() => {
        if ((isLocked || isTyping) && typewriterRef.current) {
            typewriterRef.current.stop();
            setIsTypewriterActive(false);
        }
    }, [isLocked, isTyping]);
    
    // Handle focus events to control animation
    const handleFocus = () => {
        if (typewriterRef.current && !displayValue) {
            // Pause animation while focused if empty
            typewriterRef.current.stop();
            setIsTypewriterActive(false);
        }
    };
    
    const handleBlur = () => {
        if (!displayValue && !isLocked && !isTyping && !prefersReducedMotion()) {
            // Restart animation on blur if still empty
            if (inputRef.current) {
                setIsTypewriterActive(true);
                // Enable sound effects on user interaction (optional)
                const enableSounds = localStorage.getItem('typewriter-sounds') === 'true';
                typewriterRef.current = startTypewriter(
                    inputRef.current,
                    suggestedPrompts,
                    setPlaceholderText,
                    enableSounds
                );
            }
        }
    };

    // Dynamic classes based on typewriter state
    const getTextareaClasses = () => {
        const baseClasses = 'prompt-textarea transition-all duration-300 ease-in-out';
        const typewriterClasses = isTypewriterActive 
            ? 'font-mono text-base tracking-wide' 
            : 'font-sans text-sm';
        const improvingClasses = isTyping && !isTypewriterActive
            ? 'improving-typewriter'
            : '';
        const stateClasses = isLocked || isTyping 
            ? 'opacity-70 cursor-not-allowed' 
            : '';
        
        return `${baseClasses} ${typewriterClasses} ${improvingClasses} ${stateClasses}`;
    };

    return (
        React.createElement('div', { className: 'w-full relative prompt-input-container' },
            React.createElement('label', {
                htmlFor: 'userPromptInput',
                className: 'block text-sm font-medium text-gray-300 mb-3'
            }, 'Enter Your Thumbnail Prompt:'),
            
            // Enhanced container for wider, more cinematic feel
            React.createElement('div', {
                className: 'prompt-input-wrapper relative'
            },
            React.createElement('textarea', {
                id: 'userPromptInput',
                ref: inputRef,
                className: getTextareaClasses(),
                rows: isTypewriterActive ? '3' : '4',
                placeholder: placeholderText,
                value: isTyping ? displayValue + (cursorVisible ? '|' : '') : displayValue,
                onChange,
                onFocus: handleFocus,
                onBlur: handleBlur,
                disabled: isLocked || isTyping,
                'aria-label': 'Enter your thumbnail prompt here',
                'aria-disabled': isLocked || isTyping,
                style: {
                    minHeight: isTypewriterActive ? '120px' : '135px',
                    maxWidth: '100%',
                    width: '100%'
                }
            }),
            
            // Cinematic glow effect during typewriter animation
            isTypewriterActive && React.createElement('div', {
                className: 'absolute inset-0 rounded-lg pointer-events-none',
                style: {
                    background: 'linear-gradient(45deg, transparent, rgba(139, 92, 246, 0.05), transparent)',
                    animation: 'pulse 3s ease-in-out infinite'
                }
            }),
            
            // Improve Prompt button
            React.createElement('button', {
                type: 'button',
                onClick: () => !isLocked && onImprovePrompt(),
                className: 'prompt-improve-btn control-panel-improve-btn absolute bottom-3 right-3 text-xs text-gray-400 hover:text-purple-300 flex items-center gap-1 focus:outline-none transition-all duration-200 hover:scale-105',
                disabled: isLocked || isTyping,
                'aria-label': 'Improve prompt automatically'
            },
                React.createElement('span', {
                    className: 'improve-btn-icon iconify',
                    id: 'improve-btn-icon',
                    'data-icon': 'solar:stars-outline',
                    style: { width: '16px', height: '16px' }
                }),
                'improve'
            ),
            ),
            
            isLocked && React.createElement('div', {
                id: 'prompt-lock-tooltip',
                className: 'absolute top-2 right-2 p-2 bg-yellow-500 text-black text-xs rounded-md shadow-md flex items-center gap-1 group',
                role: 'note'
            },
                React.createElement('span', {
                    className: 'iconify',
                    'data-icon': 'solar:lock-keyhole-bold',
                    style: { width: '16px', height: '16px' }
                }),
                'Locked',
                React.createElement('div', {
                    className: 'absolute bottom-full right-0 mb-2 w-72 bg-gray-800 text-gray-100 text-xs rounded-md p-2 shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity duration-200 z-50',
                    role: 'tooltip'
                }, 'Prompt is locked while a template is active. Use the controls in the sidebar to customize the thumbnail. Clear the template to edit this prompt.')
            )
        )
    );
};

const LegacyControlPanel = ({
    includePerson,
    includeIcons,
    textOverlay,
    selectedExpression,
    handleToggleChange,
    setIncludePerson,
    setIncludeIcons,
    setTextOverlay,
    fitFullCanvas,
    setFitFullCanvas,
    showLayoutSimulator,
    setShowLayoutSimulator,
    showSafeZone,
    setShowSafeZone,
    overlayText,
    setOverlayText,
    isEditingOverlayText,
    setIsEditingOverlayText,
    textPosition,
    setTextPosition,
    selectedTextSize,
    setSelectedTextSize,
    selectedFontFamily,
    setSelectedFontFamily,
    selectedGender,
    setSelectedGender,
    primaryTextColor,
    setPrimaryTextColor,
    secondaryTextColor,
    setSecondaryTextColor,
    selectedPalette,
    setSelectedPalette,
    setSelectedExpression,
    customFaceImageUrl,
    setCustomFaceImageUrl,
    imageSourceType,
    handleImageSourceTypeChange,
    handleFileUpload,
    setErrorMsg,
    backgroundCustomizationEnabled,
    setBackgroundCustomizationEnabled,
    selectedBackgroundType,
    selectedBackgroundStyleId,
    handleBackgroundStyleSelect,
    selectedSolidBgColor,
    handleSolidBgColorChange,
}) => {
    const createToggle = (label, id, checked, setter, tooltip = null) => {
        const handleKeyDown = (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                handleToggleChange(setter);
                e.preventDefault();
            }
        };
        return React.createElement('div', { className: 'toggle-row flex items-center justify-between py-2', id: `toggle-${id}-row` },
            React.createElement('div', { className: 'flex items-center gap-2' },
                React.createElement('label', { htmlFor: id, className: 'toggle-label text-sm font-medium text-gray-300 cursor-pointer' }, label),
                tooltip && React.createElement('div', { 
                    className: 'relative group', 
                    tabIndex: 0, 
                    'aria-label': 'Information about this setting',
                    role: 'button'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        stroke: 'currentColor',
                        className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                        })
                    ),
                    React.createElement('div', {
                        className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                        role: 'tooltip',
                        'aria-hidden': 'true'
                    },
                        React.createElement('div', {
                            className: 'flex items-start gap-2'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                fill: 'none',
                                viewBox: '0 0 24 24',
                                stroke: 'currentColor',
                                className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                            },
                                React.createElement('path', {
                                    strokeLinecap: 'round',
                                    strokeLinejoin: 'round',
                                    strokeWidth: 2,
                                    d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                })
                            ),
                            React.createElement('p', null, tooltip)
                        ),
                        React.createElement('div', {
                            className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                        })
                    )
                )
            ),
            React.createElement('button', {
                id,
                role: 'switch',
                'aria-checked': checked,
                onClick: () => handleToggleChange(setter),
                onKeyDown: handleKeyDown,
                tabIndex: '0',
                className: `toggle-switch ${checked ? 'bg-purple-600' : 'bg-gray-600'} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900`
            },
                React.createElement('span', { className: 'sr-only' }, 'Use setting'),
                React.createElement('span', {
                    'aria-hidden': 'true',
                    className: `toggle-circle ${checked ? 'translate-x-5' : 'translate-x-0'} inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`
                })
            )
        );
    };

    const createFaceUploadSection = () => {
        // Use a ref for the file input to programmatically clear it
        const fileInputRef = React.useRef(null);

        const currentTempUrl = imageSourceType === 'url' ? customFaceImageUrl : ''; // Only prefill if type is URL and there's a URL
        const [tempUrl, setTempUrl] = React.useState(currentTempUrl);

        React.useEffect(() => {
            // If switching to URL mode and customFaceImageUrl is a data URL (from upload), clear tempUrl
            if (imageSourceType === 'url' && customFaceImageUrl && customFaceImageUrl.startsWith('data:image')) {
                setTempUrl('');
            } else if (imageSourceType === 'url') {
                setTempUrl(customFaceImageUrl || '');
            }
            // If switching to upload, tempUrl is not directly used for display of the uploaded image path
        }, [imageSourceType, customFaceImageUrl]);

        const handleSetFaceImageFromUrl = () => {
            if (tempUrl.trim() && imageSourceType === 'url') {
                // Basic URL validation: accept any http(s) URL; still warn if not starting with http(s)
                const urlTrimmed = tempUrl.trim();
                if (!/^https?:\/\//i.test(urlTrimmed)) {
                    setErrorMsg("Invalid image URL. Must start with http:// or https://");
                    return;
                }
                setCustomFaceImageUrl(tempUrl.trim());
                setErrorMsg('');
            }
        };

        const handleRemoveFaceImage = () => {
            setCustomFaceImageUrl('');
            setTempUrl('');
            if (fileInputRef.current) {
                fileInputRef.current.value = null; // Clear the file input
            }
            setErrorMsg('');
        };
        
        const handleUrlInputChange = (e) => {
            setTempUrl(e.target.value);
        };

        const tooltipText = "Replace AI face: Upload your headshot or paste an image URL (JPEG/PNG, 2MB max).";

        if (!includePerson) return null;

        return React.createElement('div', { className: 'face-upload-section py-2 border-t border-gray-700 mt-2 pt-3', id: 'face-upload-section' },
            React.createElement('div', { className: 'flex items-center justify-between mb-2'},
                React.createElement('label', { className: 'text-sm font-medium text-gray-300' }, '🧑 Custom Face Image Source:'),
                React.createElement('div', { 
                    className: 'relative group', 
                    tabIndex: 0, 
                    'aria-label': 'Information about custom face images',
                    role: 'button'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        stroke: 'currentColor',
                        className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                        })
                    ),
                    React.createElement('div', {
                        className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                        role: 'tooltip',
                        'aria-hidden': 'true'
                    },
                        React.createElement('div', {
                            className: 'flex items-start gap-2'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                fill: 'none',
                                viewBox: '0 0 24 24',
                                stroke: 'currentColor',
                                className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                            },
                                React.createElement('path', {
                                    strokeLinecap: 'round',
                                    strokeLinejoin: 'round',
                                    strokeWidth: 2,
                                    d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                })
                            ),
                            React.createElement('p', null, tooltipText)
                        ),
                        React.createElement('div', {
                            className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                        })
                    )
                )
            ),
            // Radio buttons for source type
            React.createElement('div', { className: 'flex items-center gap-4 mb-3' },
                React.createElement('label', { htmlFor: 'sourceTypeUpload', className: 'flex items-center cursor-pointer text-sm text-gray-300' },
                    React.createElement('input', {
                        type: 'radio',
                        id: 'sourceTypeUpload',
                        name: 'imageSourceType',
                        value: 'upload',
                        checked: imageSourceType === 'upload',
                        onChange: () => handleImageSourceTypeChange('upload'),
                        className: 'form-radio h-4 w-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500 cursor-pointer'
                    }),
                    React.createElement('span', { className: 'ml-2' }, '🖼 Upload from Device')
                ),
                React.createElement('label', { htmlFor: 'sourceTypeUrl', className: 'flex items-center cursor-pointer text-sm text-gray-300' },
                    React.createElement('input', {
                        type: 'radio',
                        id: 'sourceTypeUrl',
                        name: 'imageSourceType',
                        value: 'url',
                        checked: imageSourceType === 'url',
                        onChange: () => handleImageSourceTypeChange('url'),
                        className: 'form-radio h-4 w-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500 cursor-pointer'
                    }),
                    React.createElement('span', { className: 'ml-2' }, '🌐 Import via URL')
                )
            ),

            // Conditional input based on imageSourceType
            imageSourceType === 'upload' && React.createElement('div', { className: 'flex flex-col gap-2 mt-1' },
                React.createElement('input', {
                    type: 'file',
                    id: 'customFaceFileInput',
                    ref: fileInputRef, // Assign ref
                    accept: '.jpg, .jpeg, .png',
                    onChange: handleFileUpload, // This is passed from App component
                    className: 'block w-full text-sm text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-purple-600 file:text-white hover:file:bg-purple-700 cursor-pointer focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900'
                }),
                React.createElement('p', {className: 'text-xs text-gray-500 mt-1'}, "Max 2MB. JPEG or PNG.")
            ),

            imageSourceType === 'url' && React.createElement('div', { className: 'flex items-center gap-2 mt-1' },
                React.createElement('input', {
                    type: 'text',
                    id: 'customFaceUrlInput',
                    placeholder: 'Paste image URL here... (e.g., https://example.com/face.jpg)',
                    value: tempUrl,
                    onChange: handleUrlInputChange,
                    onBlur: handleSetFaceImageFromUrl, // Validate and set on blur
                    className: 'flex-grow p-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 text-gray-100 text-sm'
                }),
                React.createElement('button', {
                    type: 'button',
                    onClick: handleSetFaceImageFromUrl,
                    className: 'px-3 py-2 text-xs bg-purple-600 hover:bg-purple-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500'
                }, 'Set URL')
            ),

            // Preview and Remove button (common for both sources)
            customFaceImageUrl && React.createElement('div', { className: 'mt-3 flex flex-col items-center gap-2 p-2 border border-gray-700 rounded-md bg-gray-700/30' },
                 React.createElement('p', { className: 'text-xs text-gray-400 self-start' }, 'Face Preview:'),
                React.createElement('img', { 
                    src: customFaceImageUrl, 
                    alt: 'Custom Face Preview', 
                    className: 'w-20 h-20 rounded-full object-cover border-2 border-purple-500 shadow-md',
                    onError: (e) => { 
                        e.target.style.display='none'; // Hide img tag on error
                        // Optionally, show a placeholder or error message next to it
                        if (!document.getElementById('img-error-msg')){
                            const errorP = document.createElement('p');
                            errorP.id = 'img-error-msg';
                            errorP.className = 'text-xs text-red-400';
                            errorP.textContent = 'Preview unavailable. Check URL or file.';
                            e.target.parentNode.appendChild(errorP);
                        }
                    },
                    onLoad: (e) => {
                         // Remove any previous error message on successful load
                        const errorMsgEl = document.getElementById('img-error-msg');
                        if (errorMsgEl) errorMsgEl.remove();
                        e.target.style.display=''; // Ensure img is visible
                    }
                }),
                React.createElement('button', {
                    type: 'button',
                    onClick: handleRemoveFaceImage,
                    className: 'mt-1 px-3 py-1 text-xs bg-red-600 hover:bg-red-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-red-500'
                }, 'Remove Image')
            ),
            React.createElement('p', {className: 'text-xs text-gray-500 mt-2'}, "Note: Uploaded face is used temporarily for generation and not stored, unless image is from an external URL you provided.")
        );
    };

    const createMoodAndExpressionPicker = () => {
        if (!includePerson) return null;

        const expressionOptions = [
            { emoji: '🙂', label: 'Default', value: 'Default' },
            { emoji: '😊', label: 'Happy', value: 'Happy' },
            { emoji: '😳', label: 'Shocked', value: 'Shocked' },
            { emoji: '😍', label: 'Loved', value: 'Loved' },
            { emoji: '🤔', label: 'Thinking', value: 'Thinking' },
            { emoji: '😠', label: 'Angry', value: 'Angry' },
            { emoji: '😢', label: 'Crying', value: 'Crying' },
            { emoji: '😆', label: 'Laughing', value: 'Laughing' },
            { emoji: '😐', label: 'Neutral', value: 'Neutral' },
            { emoji: '😎', label: 'Proud', value: 'Proud' }
        ];

        const handleExpressionClick = (newValue) => {
            setSelectedExpression(newValue);
        };

        const handleExpressionKeyDown = (e, newValue) => {
            if (e.key === 'Enter' || e.key === ' ') {
                setSelectedExpression(newValue);
                e.preventDefault();
            }
        };

        const buttons = expressionOptions.map(option =>
            React.createElement('button', {
                key: option.value,
                type: 'button',
                onClick: () => handleExpressionClick(option.value),
                onKeyDown: (e) => handleExpressionKeyDown(e, option.value),
                title: option.label,
                className: `flex flex-col items-center justify-center p-2 rounded-lg border-2 text-center ${selectedExpression === option.value ? 'border-purple-500 bg-purple-700' : 'border-gray-600 bg-gray-700 hover:border-purple-400'} transition-all focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 w-full`,
                id: `expression-btn-${option.value.toLowerCase().replace(/\s+/g, '-')}`,
                'aria-label': `${option.emoji} ${option.label}`,
                'aria-pressed': selectedExpression === option.value
            },
                React.createElement('span', { 'aria-hidden': 'true', className: 'text-3xl' }, option.emoji),
                React.createElement('span', { className: 'block text-xs mt-1 text-gray-300' }, option.label)
            )
        );

        return React.createElement('div', { className: 'mood-expression-picker-section py-2', id: 'mood-expression-picker-section' },
            React.createElement('label', { className: 'mood-expression-picker-label block text-sm font-medium text-gray-300 mb-2' }, 'Mood & Expression:'),
            React.createElement('div', { className: 'mood-expression-picker-grid grid grid-cols-5 sm:grid-cols-5 gap-2' }, buttons)
        );
    };

    const createTextOverlayToggle = () => {
        const label = 'Thumbnail Overlay Text';
        const id = 'toggleText';
        const checked = textOverlay;
        const setter = setTextOverlay;
        const tooltip = "Edit the main text for your thumbnail. Use 2–3 impactful words per line, arranged in a pyramid shape for best results. Leave blank to auto-generate from your main prompt.";

        const handleKeyDown = (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                handleToggleChange(setter);
                e.preventDefault();
            }
        };

        // Function to highlight placeholders in the overlay text editor
        const renderHighlightedTextArea = () => {
            const placeholders = findPlaceholders(overlayText);
            
            if (placeholders.length === 0) {
                // No placeholders, just render the normal textarea
                return React.createElement('textarea', {
                    id: 'overlayTextInput',
                    className: 'overlay-textarea w-full p-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:ring-purple-500 focus:border-purple-500 text-gray-100 resize-y text-sm',
                    rows: '3',
                    placeholder: 'YOUR VIDEO\nTITLE\nHERE',
                    value: overlayText,
                    onChange: (e) => setOverlayText(e.target.value),
                    'aria-label': 'Edit custom overlay text'
                });
            }

            return React.createElement('div', { className: 'relative' },
                // The actual editable textarea (invisible but functional)
                React.createElement('textarea', {
                    id: 'overlayTextInput',
                    className: 'overlay-textarea w-full p-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:ring-purple-500 focus:border-purple-500 text-gray-100 resize-y text-sm relative z-10',
                    rows: '3',
                    placeholder: 'YOUR VIDEO\nTITLE\nHERE',
                    value: overlayText,
                    onChange: (e) => setOverlayText(e.target.value),
                    'aria-label': 'Edit custom overlay text with placeholders'
                }),
                
                // Info about placeholders
                placeholders.length > 0 && React.createElement('div', { 
                    className: 'mt-1 text-xs text-yellow-400 flex items-center gap-1'
                }, 
                    React.createElement('svg', { 
                        xmlns: 'http://www.w3.org/2000/svg', 
                        fill: 'none', 
                        viewBox: '0 0 24 24', 
                        strokeWidth: 1.5, 
                        stroke: 'currentColor', 
                        className: 'w-4 h-4' 
                    },
                        React.createElement('path', { 
                            strokeLinecap: 'round', 
                            strokeLinejoin: 'round', 
                            d: 'M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z' 
                        })
                    ),
                    `Replace ${placeholders.length > 1 ? 'placeholders' : 'placeholder'} like ${placeholders.map(p => p.placeholder).join(', ')} with your content`
                )
            );
        };

        return React.createElement('div', { className: 'overlay-text-section flex flex-col gap-2', id: 'overlay-text-section' },
            React.createElement('div', { className: 'toggle-row flex items-center justify-between py-2', id: `toggle-${id}-row` },
                React.createElement('div', { className: 'flex items-center gap-2' },
                    React.createElement('label', { htmlFor: id, className: 'toggle-label text-sm font-medium text-gray-300 cursor-pointer' }, label),
                    tooltip && React.createElement('div', { 
                        className: 'relative group', 
                        tabIndex: 0, 
                        'aria-label': 'Information about this setting',
                        role: 'button'
                    },
                        React.createElement('svg', {
                            xmlns: 'http://www.w3.org/2000/svg',
                            fill: 'none',
                            viewBox: '0 0 24 24',
                            stroke: 'currentColor',
                            className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                strokeWidth: 2,
                                d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                            })
                        ),
                        React.createElement('div', {
                            className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                            role: 'tooltip',
                            'aria-hidden': 'true'
                        },
                            React.createElement('div', {
                                className: 'flex items-start gap-2'
                            },
                                React.createElement('svg', {
                                    xmlns: 'http://www.w3.org/2000/svg',
                                    fill: 'none',
                                    viewBox: '0 0 24 24',
                                    stroke: 'currentColor',
                                    className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                                },
                                    React.createElement('path', {
                                        strokeLinecap: 'round',
                                        strokeLinejoin: 'round',
                                        strokeWidth: 2,
                                        d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                    })
                                ),
                                React.createElement('p', null, tooltip)
                            ),
                            React.createElement('div', {
                                className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                            })
                        )
                    )
                ),
                React.createElement('div', { className: 'flex items-center gap-2' },
                    textOverlay && React.createElement('button', {
                        type: 'button',
                        onClick: () => setIsEditingOverlayText(prev => !prev),
                        className: `edit-overlay-btn px-2 py-1 text-xs rounded ${isEditingOverlayText ? 'bg-purple-700 text-white' : 'bg-gray-600 text-gray-300 hover:bg-purple-500'} transition-colors`,
                        id: 'edit-overlay-btn',
                        'aria-label': isEditingOverlayText ? 'Close Text Editor' : 'Edit Overlay Text'
                    }, isEditingOverlayText ? 'Close' : 'Edit'),
                    React.createElement('button', {
                        id,
                        role: 'switch',
                        'aria-checked': checked,
                        onClick: () => handleToggleChange(setter),
                        onKeyDown: handleKeyDown,
                        tabIndex: '0',
                        className: `toggle-switch ${checked ? 'bg-purple-600' : 'bg-gray-600'} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900`
                    },
                        React.createElement('span', { className: 'sr-only' }, 'Use setting'),
                        React.createElement('span', { 'aria-hidden': 'true', className: `toggle-circle ${checked ? 'translate-x-5' : 'translate-x-0'} inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out` })
                    )
                )
            ),
            textOverlay && isEditingOverlayText && React.createElement('div', { className: 'overlay-text-editor mt-1 w-full', id: 'overlay-text-editor' },
                React.createElement('label', { htmlFor: 'overlayTextInput', className: 'overlay-text-editor-label block text-xs font-medium text-gray-400 mb-1' }, 'Edit overlay text (pyramid shape recommended):' ),
                renderHighlightedTextArea()
            )
        );
    };

    // Re-introduce the dropdown selector function
    const createPositionSelector = () => {
        if (!textOverlay) return null; // Only show if text overlay is enabled

        const positions = [
            "Top Left", "Top Center", "Top Right",
            "Center",
            "Bottom Left", "Bottom Center", "Bottom Right"
        ];

        const options = positions.map(pos =>
            React.createElement('option', { key: pos, value: pos }, pos)
        );

        const tooltipText = "Choose where the title text should appear. Top Right is standard. Center is good for dramatic titles.";

        return React.createElement('div', { className: 'text-position-selector py-2', id: 'text-position-selector' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1'}, // Label and Tooltip
                React.createElement('label', { htmlFor: 'textPositionSelect', className: 'text-sm font-medium text-gray-300' }, 'Text Position:'),
                React.createElement('div', { 
                    className: 'relative group', 
                    tabIndex: 0, 
                    'aria-label': 'Information about text positioning',
                    role: 'button'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        stroke: 'currentColor',
                        className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                        })
                    ),
                    React.createElement('div', {
                        className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                        role: 'tooltip',
                        'aria-hidden': 'true'
                    },
                        React.createElement('div', {
                            className: 'flex items-start gap-2'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                fill: 'none',
                                viewBox: '0 0 24 24',
                                stroke: 'currentColor',
                                className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                            },
                                React.createElement('path', {
                                    strokeLinecap: 'round',
                                    strokeLinejoin: 'round',
                                    strokeWidth: 2,
                                    d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                })
                            ),
                            React.createElement('p', null, tooltipText)
                        ),
                        React.createElement('div', {
                            className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                        })
                    )
                )
            ),
            React.createElement('select', {
                id: 'textPositionSelect',
                value: textPosition,
                onChange: (e) => setTextPosition(e.target.value),
                className: 'w-full p-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 text-gray-100 text-sm'
            },
                options
            )
        );
    };

    const createTextSizeRadioButtons = () => {
        if (!textOverlay) return null;

        const sizes = ["Small", "Medium", "Large"];
        const tooltipText = "Choose the size of the title text. Small offers a minimal, subtle look. Medium provides a balanced look, while Large offers maximum impact, especially for mobile. 'Medium' is generally recommended for YouTube thumbnails.";

        return React.createElement('div', { className: 'text-size-radios py-2', id: 'text-size-radios' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1'},
                React.createElement('label', { className: 'text-sm font-medium text-gray-300' }, 'Text Size:'),
                React.createElement('div', { 
                    className: 'relative group', 
                    tabIndex: 0, 
                    'aria-label': 'Information about text size options',
                    role: 'button'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        stroke: 'currentColor',
                        className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                        })
                    ),
                    React.createElement('div', {
                        className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                        role: 'tooltip',
                        'aria-hidden': 'true'
                    },
                        React.createElement('div', {
                            className: 'flex items-start gap-2'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                fill: 'none',
                                viewBox: '0 0 24 24',
                                stroke: 'currentColor',
                                className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                            },
                                React.createElement('path', {
                                    strokeLinecap: 'round',
                                    strokeLinejoin: 'round',
                                    strokeWidth: 2,
                                    d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                })
                            ),
                            React.createElement('p', null, tooltipText)
                        ),
                        React.createElement('div', {
                            className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                        })
                    )
                )
            ),
            React.createElement('div', { className: 'flex items-center gap-4 mt-1' },
                sizes.map(size => React.createElement('label', { key: size, htmlFor: `textSize-${size}`, className: 'flex items-center cursor-pointer text-sm text-gray-300' },
                    React.createElement('input', {
                        type: 'radio',
                        id: `textSize-${size}`,
                        name: 'selectedTextSize',
                        value: size,
                        checked: selectedTextSize === size,
                        onChange: () => setSelectedTextSize(size),
                        className: 'form-radio h-4 w-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500 cursor-pointer'
                    }),
                    React.createElement('span', { className: 'ml-2' }, size)
                ))
            )
        );
    };

    const createFontSelector = () => {
        if (!textOverlay) return null;
        const fontOptions = ['Impact', 'Arial', 'Verdana', 'Georgia', 'Comic Sans MS']; // Added Comic Sans for fun
        const tooltipText = "Select the primary font family for the overlay text. Ensure it complements the thumbnail's style.";

        return React.createElement('div', { className: 'font-selector-section py-2', id: 'font-selector-section' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1'},
                React.createElement('label', { htmlFor: 'fontFamilySelect', className: 'text-sm font-medium text-gray-300' }, 'Font Family:'),
                React.createElement('div', { 
                    className: 'relative group', 
                    tabIndex: 0, 
                    'aria-label': 'Information about font selection',
                    role: 'button'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        stroke: 'currentColor',
                        className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                        })
                    ),
                    React.createElement('div', {
                        className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                        role: 'tooltip',
                        'aria-hidden': 'true'
                    },
                        React.createElement('div', {
                            className: 'flex items-start gap-2'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                fill: 'none',
                                viewBox: '0 0 24 24',
                                stroke: 'currentColor',
                                className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                            },
                                React.createElement('path', {
                                    strokeLinecap: 'round',
                                    strokeLinejoin: 'round',
                                    strokeWidth: 2,
                                    d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                })
                            ),
                            React.createElement('p', null, tooltipText)
                        ),
                        React.createElement('div', {
                            className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                        })
                    )
                )
            ),
            React.createElement('select', {
                id: 'fontFamilySelect',
                value: selectedFontFamily,
                onChange: (e) => setSelectedFontFamily(e.target.value),
                className: 'w-full p-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 text-gray-100 text-sm'
            },
                fontOptions.map(font => React.createElement('option', { key: font, value: font }, font))
            )
        );
    };

    const createGenderSelector = () => {
        if (!includePerson) return null;
        const genderOptions = [
            { emoji: '🤖', label: 'Auto', value: 'Auto' },
            { emoji: '👨', label: 'Male', value: 'Male' },
            { emoji: '👩', label: 'Female', value: 'Female' },
            { emoji: '🧑', label: 'Non-binary', value: 'Non-binary' }
        ];
        const tooltipText = "Specify the gender of the person in the thumbnail. 'Auto' lets the AI decide. This is only active if 'Include Person' is ON.";

        return React.createElement('div', { className: 'gender-selector-section py-2', id: 'gender-selector-section' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1'},
                React.createElement('label', { htmlFor: 'genderSelect', className: 'text-sm font-medium text-gray-300' }, 'Preferred Gender:'),
                React.createElement('div', { 
                    className: 'relative group', 
                    tabIndex: 0, 
                    'aria-label': 'Information about gender selection',
                    role: 'button'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        stroke: 'currentColor',
                        className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                        })
                    ),
                    React.createElement('div', {
                        className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                        role: 'tooltip',
                        'aria-hidden': 'true'
                    },
                        React.createElement('div', {
                            className: 'flex items-start gap-2'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                fill: 'none',
                                viewBox: '0 0 24 24',
                                stroke: 'currentColor',
                                className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                            },
                                React.createElement('path', {
                                    strokeLinecap: 'round',
                                    strokeLinejoin: 'round',
                                    strokeWidth: 2,
                                    d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                })
                            ),
                            React.createElement('p', null, tooltipText)
                        ),
                        React.createElement('div', {
                            className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                        })
                    )
                )
            ),
            React.createElement('div', { className: 'grid grid-cols-4 gap-2 mt-2', role: 'radiogroup', 'aria-label': 'Gender selection' },
                genderOptions.map(gender => React.createElement('button', {
                    key: gender.value,
                    onClick: () => setSelectedGender(gender.value),
                    className: `gender-card flex flex-col items-center justify-center p-2 ${selectedGender === gender.value ? 'bg-purple-700 border-purple-500' : 'bg-gray-700 border-gray-600'} border rounded-md shadow-sm hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50`,
                    'aria-pressed': selectedGender === gender.value,
                    'aria-label': gender.label,
                    disabled: !includePerson
                },
                    React.createElement('span', { className: 'text-2xl mb-1' }, gender.emoji),
                    React.createElement('span', { className: 'text-xs font-medium' }, gender.label)
                ))
            )
        );
    };
    
    const createTextColorPickers = () => {
        if (!textOverlay) return null;
        const tooltipText = "Choose primary and secondary colors for the text overlay. High contrast is recommended for readability (e.g., light text on dark glow/shadow, or vice-versa).";

        // selectedPalette is managed by parent component via props

        const handleSelectPalette = (paletteId, colors) => {
            if (!colors || colors.length < 2) return;
            
            // Update the app state with the selected palette colors
            setPrimaryTextColor(colors[0]);
            setSecondaryTextColor(colors[1]);
            
            // Track which palette is selected
            setSelectedPalette(paletteId);
        };
        
        // When manual color picker changes, set to custom
        const handleManualColorChange = (isPrimary, color) => {
            if (isPrimary) {
                setPrimaryTextColor(color);
            } else {
                setSecondaryTextColor(color);
            }
            setSelectedPalette('custom');
        };

        return React.createElement('div', { className: 'text-color-pickers-section py-2', id: 'text-color-pickers-section' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1'},
                React.createElement('label', { className: 'text-sm font-medium text-gray-300' }, 'Text Colors:'),
                React.createElement('div', { className: 'tooltip-wrapper relative group' },
                     React.createElement('svg', { className: 'tooltip-icon w-4 h-4 text-gray-400 hover:text-purple-400 cursor-help transition-colors' , xmlns: 'http://www.w3.org/2000/svg', fill: 'none', viewBox: '0 0 24 24', strokeWidth: 1.5, stroke: 'currentColor' }, React.createElement('path', { strokeLinecap: 'round', strokeLinejoin: 'round', d: 'M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z' })),
                     React.createElement('div', { className: 'tooltip-content absolute bottom-full left-1/2 z-50 mb-2 -translate-x-1/2 transform rounded-md bg-purple-900 px-3 py-2 text-xs text-white shadow-lg opacity-0 invisible transition-all duration-300 ease-in-out delay-150 group-hover:opacity-100 group-hover:visible', style: { minWidth: '200px', maxWidth: '300px' }}, React.createElement('div', { className: 'tooltip-arrow absolute -bottom-2 left-1/2 h-4 w-4 -translate-x-1/2 transform rotate-45 bg-purple-900' }), React.createElement('div', { className: 'tooltip-text relative z-10 flex flex-col gap-1' }, React.createElement('p', { className: 'tooltip-title font-medium text-purple-200 mb-1' }, 'Text Color Customization'), React.createElement('p', { className: 'tooltip-description leading-relaxed' }, tooltipText)))
                )
            ),
            // Add the new color palette presets component
            React.createElement(ColorPalettePresets, {
                onSelectPalette: handleSelectPalette,
                currentPalette: selectedPalette,
                primaryColor: primaryTextColor,
                secondaryColor: secondaryTextColor
            }),
            React.createElement('div', { className: 'grid grid-cols-2 gap-3 mt-1' },
                React.createElement('div', null,
                    React.createElement('label', { htmlFor: 'primaryTextColorPicker', className: 'block text-xs font-medium text-gray-400 mb-1' }, 'Primary:'),
                    React.createElement('input', {
                        type: 'color',
                        id: 'primaryTextColorPicker',
                        value: primaryTextColor,
                        onChange: (e) => handleManualColorChange(true, e.target.value),
                        className: 'w-full h-10 p-1 bg-gray-700 border border-gray-600 rounded-md cursor-pointer focus:ring-purple-500 focus:border-purple-500'
                    })
                ),
                React.createElement('div', null,
                    React.createElement('label', { htmlFor: 'secondaryTextColorPicker', className: 'block text-xs font-medium text-gray-400 mb-1' }, 'Secondary:'),
                    React.createElement('input', {
                        type: 'color',
                        id: 'secondaryTextColorPicker',
                        value: secondaryTextColor,
                        onChange: (e) => handleManualColorChange(false, e.target.value),
                        className: 'w-full h-10 p-1 bg-gray-700 border border-gray-600 rounded-md cursor-pointer focus:ring-purple-500 focus:border-purple-500'
                    })
                )
            ),
            // Recent colors - Show the 3 most recently used color combinations
            React.createElement(RecentlyUsedColors, {
                onSelectColors: (primary, secondary) => {
                    setPrimaryTextColor(primary);
                    setSecondaryTextColor(secondary);
                    setSelectedPalette('custom');
                }
            })
        );
    };

    // Text Color Palette Presets Component
    const ColorPalettePresets = ({ 
        onSelectPalette, 
        currentPalette,
        primaryColor,
        secondaryColor 
    }) => {
        // Store element ref for scrolling
        const scrollContainerRef = React.useRef(null);
        
        // Track if we're in a custom state (user manually changed colors)
        const isCustomColor = React.useMemo(() => {
            if (!currentPalette) return true;
            if (currentPalette === 'custom') return true;
            
            // Find the selected palette
            const selectedPalette = TEXT_COLOR_PALETTES.find(p => p.id === currentPalette);
            if (!selectedPalette) return true;
            
            // Compare colors to see if they match the palette
            return !(
                selectedPalette.colors[0] === primaryColor && 
                selectedPalette.colors[1] === secondaryColor
            );
        }, [currentPalette, primaryColor, secondaryColor]);
        
        // Horizontal scroll with keyboard
        const handleKeyDown = (e) => {
            const container = scrollContainerRef.current;
            if (!container) return;
            
            const SCROLL_AMOUNT = 150;
            
            if (e.key === 'ArrowRight') {
                container.scrollLeft += SCROLL_AMOUNT;
                e.preventDefault();
            } else if (e.key === 'ArrowLeft') {
                container.scrollLeft -= SCROLL_AMOUNT;
                e.preventDefault();
            }
        };
        
        return React.createElement('div', { className: 'color-palette-presets mb-3' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1' },
                React.createElement('label', { className: 'block text-xs font-medium text-gray-400' }, 'Color Palettes:'),
                isCustomColor && React.createElement('span', { 
                    className: 'text-xs text-gray-400 bg-gray-700 px-2 py-0.5 rounded' 
                }, '🎨 Custom')
            ),
            React.createElement('div', { 
                className: 'palette-scroll-container relative',
                onKeyDown: handleKeyDown,
                tabIndex: '0'
            },
                React.createElement('div', {
                    ref: scrollContainerRef,
                    className: 'flex overflow-x-auto pb-2 space-x-2 scroll-smooth',
                    'aria-label': 'Color palette presets',
                    style: { scrollbarWidth: 'thin' }
                },
                    TEXT_COLOR_PALETTES.map(palette => {
                        const isSelected = currentPalette === palette.id;
                        return React.createElement('button', {
                            key: palette.id,
                            onClick: () => onSelectPalette(palette.id, palette.colors),
                            className: `palette-button flex-shrink-0 rounded-md p-2 ${isSelected ? 'ring-2 ring-purple-500 bg-gray-700' : 'bg-gray-800 hover:bg-gray-700'} cursor-pointer transition-all focus:outline-none focus:ring-2 focus:ring-purple-400`,
                            'aria-label': `${palette.name} color palette`,
                            'aria-pressed': isSelected,
                            style: { minWidth: '90px' }
                        },
                            React.createElement('div', { className: 'color-chips-container flex justify-center mb-1 gap-1' },
                                palette.colors.slice(0, 4).map((color, i) => 
                                    React.createElement('div', {
                                        key: `${palette.id}-color-${i}`,
                                        className: 'color-chip rounded-full border border-gray-600 w-4 h-4',
                                        style: { backgroundColor: color }
                                    })
                                )
                            ),
                            React.createElement('div', { className: 'text-center text-xs text-gray-300' }, palette.name)
                        );
                    })
                ),
                // Scroll indicators
                React.createElement('div', { 
                    className: 'absolute left-0 top-0 bottom-0 bg-gradient-to-r from-gray-800 to-transparent w-6 pointer-events-none opacity-75',
                    'aria-hidden': 'true'
                }),
                React.createElement('div', { 
                    className: 'absolute right-0 top-0 bottom-0 bg-gradient-to-l from-gray-800 to-transparent w-6 pointer-events-none opacity-75',
                    'aria-hidden': 'true'
                })
            )
        );
    };

    // Recently Used Colors Component
    const RecentlyUsedColors = ({ onSelectColors }) => {
        const [recentColors, setRecentColors] = React.useState([]);
        
        // Load recent colors on component mount
        React.useEffect(() => {
            try {
                const savedColors = JSON.parse(localStorage.getItem('recentTextColors') || '[]');
                setRecentColors(savedColors.slice(0, 3)); // Show only the 3 most recent
            } catch (e) {
                console.error('Error loading recent colors:', e);
            }
        }, []);
        
        if (recentColors.length === 0) return null;
        
        return React.createElement('div', { className: 'recent-colors-section mt-3 pt-2 border-t border-gray-700' },
            React.createElement('label', { className: 'block text-xs font-medium text-gray-400 mb-2' }, 'Recently Used:'),
            React.createElement('div', { className: 'flex gap-2 overflow-x-auto' },
                recentColors.map((colorPair, index) => 
                    React.createElement('button', {
                        key: `recent-${index}`,
                        onClick: () => onSelectColors(colorPair.primary, colorPair.secondary),
                        className: 'recent-color-pair flex-shrink-0 p-1 rounded hover:bg-gray-700 focus:outline-none focus:ring-1 focus:ring-purple-400',
                        'aria-label': `Select recent color combination ${index + 1}`
                    },
                        React.createElement('div', { className: 'flex rounded overflow-hidden border border-gray-600', style: { width: '50px', height: '20px' } },
                            React.createElement('div', { className: 'w-1/2 h-full', style: { backgroundColor: colorPair.primary } }),
                            React.createElement('div', { className: 'w-1/2 h-full', style: { backgroundColor: colorPair.secondary } })
                        )
                    )
                )
            )
        );
    };

    // Persist recently used text colors (runs once per render when colors change)
    React.useEffect(() => {
        if (!primaryTextColor || !secondaryTextColor) return;
        try {
            const recentColors = JSON.parse(localStorage.getItem('recentTextColors') || '[]');
            const existingIndex = recentColors.findIndex(pair => pair.primary === primaryTextColor && pair.secondary === secondaryTextColor);
            if (existingIndex > -1) recentColors.splice(existingIndex, 1);
            recentColors.unshift({ primary: primaryTextColor, secondary: secondaryTextColor, timestamp: Date.now() });
            if (recentColors.length > 5) recentColors.pop();
            localStorage.setItem('recentTextColors', JSON.stringify(recentColors));
        } catch (e) {
            console.error('Error saving recent colors:', e);
        }
    }, [primaryTextColor, secondaryTextColor]);

    // Duplicate background handler functions removed (now provided via props)

    // Add the new BackgroundStyleSelector component here
    const backgroundSelectorElement = React.createElement(BackgroundStyleSelector, {
        selectedStyleId: selectedBackgroundStyleId, // from App state via props
        onSelectStyle: handleBackgroundStyleSelect, // from App state via props
        selectedSolidColor: selectedSolidBgColor, // from App state via props
        onSolidColorChange: handleSolidBgColorChange // from App state via props
    });

    return React.createElement('div', { className: 'design-controls-panel p-4 bg-gray-800 rounded-lg shadow-md flex flex-col gap-3', id: 'design-controls' },
        React.createElement('h2', { className: 'design-controls-heading text-lg font-semibold text-purple-400 mb-2' }, 'Design Controls'),
        // Text Controls Group
        React.createElement('div', { className: 'control-group border border-gray-700 rounded-md p-3' },
            React.createElement('h3', { className: 'text-md font-semibold text-purple-300 mb-2' }, 'Text & Overlay'),
            createTextOverlayToggle(),
            createPositionSelector(),
            createTextSizeRadioButtons(),
            createFontSelector(),
            createTextColorPickers()
        ),
        // Subject Controls Group
        React.createElement('div', { className: 'control-group border border-gray-700 rounded-md p-3' },
            React.createElement('h3', { className: 'text-md font-semibold text-purple-300 mb-2' }, 'Subject & Scene'),
            createToggle('Include Person?', 'togglePerson', includePerson, (value) => handleToggleChange(setIncludePerson, true), "Including a person in your thumbnail can increase viewer engagement. Enable this option if your video features a presenter or reaction shots. This may increase generation complexity."),
            createFaceUploadSection(),
            createGenderSelector(),
            createMoodAndExpressionPicker(),
            createToggle('Include Icons?', 'toggleIcons', includeIcons, (value) => handleToggleChange(setIncludeIcons), "Add visual elements like icons, badges or symbols that enhance the thumbnail\'s message. Good for tech tutorials, comparisons, or step-by-step guides.")
        ),
        // Background Controls Group - UPDATED
        React.createElement('div', { className: 'control-group border border-gray-700 rounded-md p-3' },
            React.createElement('div', { className: 'flex items-center justify-between mb-2' },
                React.createElement('h3', { className: 'text-md font-semibold text-purple-300 background-section-heading', id: 'background-section-heading' }, 'Background'),
                React.createElement('button', {
                    onClick: () => setBackgroundCustomizationEnabled(prev => !prev),
                    className: `${backgroundCustomizationEnabled ? 'bg-purple-600 hover:bg-purple-700' : 'bg-gray-600 hover:bg-gray-500'} text-white text-xs px-2 py-1 rounded focus:outline-none focus:ring-2 focus:ring-purple-500`,
                    'aria-pressed': backgroundCustomizationEnabled,
                    title: backgroundCustomizationEnabled ? 'Disable background customization and use neutral default' : 'Enable background customization'
                }, backgroundCustomizationEnabled ? 'Reset to Default' : 'Customize Background')
            ),
            React.createElement('p', { className: 'text-xs text-gray-400 mb-2' }, backgroundCustomizationEnabled ? 'Choose a background style below, or reset to default.' : 'Neutral cinematic background will be used by default.'),
            React.createElement('div', { className: backgroundCustomizationEnabled ? '' : 'opacity-50 pointer-events-none' },
                backgroundSelectorElement
            )
        ),
        // Advanced & Preview Controls Group
        React.createElement('div', { className: 'control-group border border-gray-700 rounded-md p-3' },
             React.createElement('h3', { className: 'text-md font-semibold text-purple-300 mb-2' }, 'Layout & Preview'),
            createToggle('Fit Full Canvas', 'toggleFitFullCanvas', fitFullCanvas, setFitFullCanvas, "Ensures the image fills the entire thumbnail width (1280x720) without black side bars or padding. Automatically adjusts background and composition."),
            createToggle('Show Layout Simulator', 'toggleLayoutSim', showLayoutSimulator, setShowLayoutSimulator, "Shows approximate placement of elements before generation. For visualization only."),
            createToggle('Show Text-Safe Zone', 'toggleSafeZone', showSafeZone, setShowSafeZone, "Outlines the area where text will be fully visible on all devices, including mobile.")
        ),
        // Hidden toggles remain as they were
        React.createElement('div', { id: 'toggle-toggleTextPosition-row', style: { display: 'none' } },
            createToggle('Text Position', 'toggleTextPosition', textPosition === 'Top Right', setTextPosition, "Select the position of the text overlay on the thumbnail.")
        ),
    );
};

const ThumbnailPreview = ({ 
    imageURL, 
    isLoading, 
    showLayoutSimulator, 
    showSafeZone,
    // Need these for simulator
    includePerson,
    includeIcons,
    textOverlay,
    overlayText,
    selectedFontFamily,
    selectedTextSize,
    primaryTextColor,
    secondaryTextColor
 }) => {
    const placeholder = React.createElement('div', { className: 'w-full h-full flex flex-col items-center justify-center text-gray-400' },
        isLoading ? React.createElement('div', null,
            React.createElement('div', { className: 'animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-purple-500' }),
            React.createElement('p', { className: 'mt-4 text-lg' }, 'Generating Thumbnail...')
        ) : React.createElement(React.Fragment, null,
            React.createElement('img', { 
                src: '/dist/assets/empty-states/Album.svg', 
                alt: 'Album', 
                className: 'w-16 h-16 text-gray-500 mb-4' 
            }),
            React.createElement('p', { className: 'text-gray-500' }, 'Thumbnail Preview (768x512)')
        )
    );

    // Determine if simulator should be shown (before generation)
    const shouldShowSimulator = showLayoutSimulator && !imageURL && !isLoading;
    // Determine if safe zone should be shown (after generation)
    const shouldShowSafeZone = showSafeZone && imageURL && !isLoading;

    return React.createElement('div', { className: 'preview-container' }, 
        // Image or Placeholder
        imageURL ? 
            React.createElement('img', { src: imageURL, alt: 'Generated Thumbnail', className: 'generated-thumbnail' })
            : placeholder,
        
        // Layout Simulator Overlay (shows before image)
        shouldShowSimulator && React.createElement(LayoutSimulatorOverlay, { 
            includePerson,
            includeIcons,
            textOverlay,
            overlayText,
            selectedFontFamily,
            selectedTextSize,
            primaryTextColor,
            secondaryTextColor
         }),

        // Safe Zone Overlay (shows over generated image)
        shouldShowSafeZone && React.createElement(SafeZoneOverlay, null)
    );
};

// ================= Button Components =================
const DownloadButton = ({ imageURL, isLoading }) => {
    const handleDownloadClick = () => {
        if (!imageURL || isLoading) return;

        // Create a temporary link element
        const link = document.createElement('a');
        link.href = imageURL; // imageURL is already a data URL (base64)
        link.download = 'thumbnail.png'; // Desired filename

        // Programmatically click the link to trigger the download
        document.body.appendChild(link);
        link.click();

        // Clean up the temporary link
        document.body.removeChild(link);
    };

    const isDisabled = !imageURL || isLoading;

    return React.createElement('button', {
        onClick: handleDownloadClick,
        disabled: isDisabled,
        className: `px-4 py-2 bg-green-600 text-white font-medium rounded-md shadow-md ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-green-700 active:bg-green-800'} transition-colors duration-200 flex items-center justify-center gap-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-gray-900`,
        'aria-label': 'Download generated thumbnail'
    },
        React.createElement('svg', { // Heroicon: ArrowDownTrayIcon
            xmlns: 'http://www.w3.org/2000/svg',
            fill: 'none',
            viewBox: '0 0 24 24',
            strokeWidth: 1.5,
            stroke: 'currentColor',
            className: 'w-5 h-5'
        }, React.createElement('path', {
            strokeLinecap: 'round',
            strokeLinejoin: 'round',
            d: 'M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3'
        })),
        'Download'
    );
};

const GenerateButton = ({ onClick, isLoading, includePerson, textOverlay }) => {
    const isCleanBackgroundMode = !includePerson && !textOverlay;

    return React.createElement('div', { className: 'relative' }, // Wrapper for button and badge
        React.createElement('button', {
            onClick,
            disabled: isLoading,
            className: 'px-4 py-2 bg-purple-600 text-white font-medium rounded-md shadow-md hover:bg-purple-700 active:bg-purple-800 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-colors duration-200 disabled:opacity-50 flex items-center justify-center gap-2'
        }, 
            isLoading ? 
            React.createElement(React.Fragment, null,
                React.createElement('svg', {
                    className: 'animate-spin -ml-1 mr-2 h-5 w-5 text-white',
                    xmlns: 'http://www.w3.org/2000/svg',
                    fill: 'none',
                    viewBox: '0 0 24 24'
                },
                    React.createElement('circle', {
                        className: 'opacity-25',
                        cx: '12',
                        cy: '12',
                        r: '10',
                        stroke: 'currentColor',
                        strokeWidth: '4'
                    }),
                    React.createElement('path', {
                        className: 'opacity-75',
                        fill: 'currentColor',
                        d: 'M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                    })
                ),
                React.createElement('span', null, 'Generating...')
            ) : 
            React.createElement(React.Fragment, null,
                React.createElement('svg', {
                    xmlns: 'http://www.w3.org/2000/svg',
                    fill: 'none',
                    viewBox: '0 0 24 24',
                    strokeWidth: 1.5,
                    stroke: 'currentColor',
                    className: 'w-5 h-5'
                },
                    React.createElement('path', {
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        d: 'M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z'
                    })
                ),
                React.createElement('span', null, 'Generate Thumbnail')
            )
        ),
        isCleanBackgroundMode && React.createElement('div', {
            className: 'absolute -top-2 -right-2 bg-blue-500 text-white text-[10px] font-semibold px-1.5 py-0.5 rounded-full shadow-md transform translate-x-1/2 -translate-y-1/2 whitespace-nowrap',
            title: 'Text Overlay and Include Person are both OFF'
        }, '🖼️ Clean Background')
    );
};

// ================= Overlay Components =================

const LayoutSimulatorOverlay = ({ includePerson, includeIcons, textOverlay, overlayText, selectedFontFamily, selectedTextSize, primaryTextColor, secondaryTextColor }) => {
    if (!includePerson && !includeIcons && !textOverlay) {
        return null; // Don't show if nothing is toggled
    }

    const overlayStyle = {
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none', // Allow clicks to pass through
    };

    const zoneStyle = {
        position: 'absolute',
        border: '2px dashed rgba(255, 255, 255, 0.5)',
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '10px',
        color: 'rgba(255, 255, 255, 0.7)',
        padding: '2px 4px',
        borderRadius: '4px',
    };

    return React.createElement('div', { style: overlayStyle },
        // Person Zone (Example: Left side)
        includePerson && React.createElement('div', {
            style: { ...zoneStyle, top: '10%', left: '5%', width: '40%', height: '80%' }
        }, 'PERSON'),

        // Text Overlay Zone (Example: Top Right)
        textOverlay && React.createElement('div', {
            style: { ...zoneStyle, top: '10%', right: '5%', width: '45%', height: '25%' }
        }, 'TEXT OVERLAY'),

        // Icons Zone (Example: Floating around)
        includeIcons && React.createElement(React.Fragment, null,
            React.createElement('div', {
                style: { ...zoneStyle, top: '30%', right: '10%', width: '15%', height: '15%', borderRadius: '50%' }
            }, 'ICON'),
            React.createElement('div', {
                style: { ...zoneStyle, bottom: '15%', left: '40%', width: '10%', height: '10%', borderRadius: '50%' }
            }, 'ICON')
        )
    );
};

const SafeZoneOverlay = () => {
    const overlayStyle = {
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        border: '2px solid rgba(255, 0, 0, 0.6)', // Outer edge for reference
    };

    const safeZoneStyle = {
        position: 'absolute',
        // 64px margin on a 1280x720 viewport roughly translates to percentages
        top: `${(64 / 720) * 100}%`,
        left: `${(64 / 1280) * 100}%`,
        width: `${100 - 2 * (64 / 1280) * 100}%`,
        height: `${100 - 2 * (64 / 720) * 100}%`,
        border: '3px dashed rgba(255, 255, 0, 0.8)', // Yellow dashed line for safe zone
        boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.3)', // Dim outside area
        display: 'flex',
        alignItems: 'flex-start',
        justifyContent: 'center',
    };

    const labelStyle = {
        backgroundColor: 'rgba(255, 255, 0, 0.8)',
        color: 'black',
        padding: '2px 6px',
        fontSize: '10px',
        fontWeight: 'bold',
        borderRadius: '3px',
        marginTop: '5px'
    };

    return React.createElement('div', { style: overlayStyle },
        React.createElement('div', { style: safeZoneStyle },
            React.createElement('span', { style: labelStyle }, 'Text Safe Zone')
        )
    );
};

// ================= Quality Toggle Component =================
const QualityToggle = ({ isLowCostMode, onToggle }) => {
    const handleKeyDown = (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
            onToggle();
            e.preventDefault();
        }
    };

    const bgColor = isLowCostMode ? 'bg-green-600' : 'bg-red-600';
    const hoverBgColor = isLowCostMode ? 'hover:bg-green-700' : 'hover:bg-red-700';
    const ringColor = isLowCostMode ? 'focus:ring-green-500' : 'focus:ring-red-500';

    return React.createElement('div', { className: 'flex items-center justify-between py-2 px-4 bg-gray-800 rounded-lg shadow-md mt-4 w-full' }, // Added w-full
        React.createElement('div', { className: 'flex items-center gap-2 group relative' }, // Group label and tooltip icon
            React.createElement('label', { 
                htmlFor: 'qualityToggle', 
                className: 'text-sm font-medium text-gray-300 cursor-pointer' 
            }, isLowCostMode ? 'Low-Cost Mode (Testing)' : 'High-Quality Mode (Export)'),
            React.createElement('div', { 
                className: 'relative group', 
                tabIndex: 0, 
                'aria-label': 'Information about quality settings',
                role: 'button'
            },
                React.createElement('svg', {
                xmlns: 'http://www.w3.org/2000/svg', 
                fill: 'none', 
                viewBox: '0 0 24 24', 
                stroke: 'currentColor', 
                    className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
            },
                React.createElement('path', { 
                    strokeLinecap: 'round', 
                    strokeLinejoin: 'round', 
                        strokeWidth: 2,
                        d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                })
            ),
            React.createElement('div', { 
                    className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                    role: 'tooltip',
                    'aria-hidden': 'true'
                },
                    React.createElement('div', {
                        className: 'flex items-start gap-2'
                    },
                        React.createElement('svg', {
                            xmlns: 'http://www.w3.org/2000/svg',
                            fill: 'none',
                            viewBox: '0 0 24 24',
                            stroke: 'currentColor',
                            className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                strokeWidth: 2,
                                d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                            })
                        ),
                        React.createElement('p', null, 'Toggle between low-cost mode for testing or high-quality mode for final exports.')
                    ),
                    React.createElement('div', {
                        className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                    })
                )
            )
        ),
        // The actual toggle switch
        React.createElement('button', {
            id: 'qualityToggle',
            role: 'switch',
            'aria-checked': !isLowCostMode, // Aria checked corresponds to 'High Quality' being ON
            onClick: onToggle,
            onKeyDown: handleKeyDown,
            tabIndex: '0',
            className: `${bgColor} ${hoverBgColor} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 ${ringColor} focus:ring-offset-2 focus:ring-offset-gray-900`
        },
            React.createElement('span', { className: 'sr-only' }, 'Toggle Image Quality Mode'),
            React.createElement('span', {
                'aria-hidden': 'true',
                className: `${!isLowCostMode ? 'translate-x-5' : 'translate-x-0'} inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`
            })
        )
    );
};

// ================= PremadeTemplatesSection Component =================
const PremadeTemplatesSection = ({ templatesData, onTemplateSelect, activeCategory, setActiveCategory }) => {
    const [modalOpen, setModalOpen] = React.useState(false);
    const [selectedCategory, setSelectedCategory] = React.useState(null);
    const [showMoreModalOpen, setShowMoreModalOpen] = React.useState(false);
    const [expandedSelectedCategory, setExpandedSelectedCategory] = React.useState(null);
    
    // Handler for opening the modal with a specific category
    const openCategoryModal = (category) => {
        setSelectedCategory(category);
        setModalOpen(true);
    };
    
    // Close modal handler
    const closeModal = () => {
        setModalOpen(false);
    };

    // Handler for opening the "Show More" modal
    const openShowMoreModal = () => {
        setShowMoreModalOpen(true);
    };
    
    // Close "Show More" modal handler
    const closeShowMoreModal = () => {
        setShowMoreModalOpen(false);
        setExpandedSelectedCategory(null);
    };

    // Handler for opening an expanded category's templates
    const openExpandedCategoryModal = (category) => {
        setExpandedSelectedCategory(category);
    };
    
    // Render a category card with modern styling
    const renderCategoryCard = (category) => {
        const isActive = activeCategory === category.id;
        const unsplashUrl = getUnsplashImageUrl(category.id);
        
        return React.createElement('div', {
            key: category.id,
            className: `template-category-card ${isActive ? 'active' : ''}`,
            onClick: () => openCategoryModal(category),
            role: 'button',
            'aria-expanded': isActive,
            'aria-controls': `templates-${category.id}`,
            id: `template-category-card-${category.id}`,
            tabIndex: 0,
            onKeyDown: (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                    openCategoryModal(category);
                }
            }
        },
            // Card content with background image (matching Figma JSON structure)
            React.createElement('div', {
                className: 'template-category-content',
                style: {
                    backgroundImage: `url(${unsplashUrl})`
                }
            }, 
                // Category label overlay (matching Figma JSON label structure)
                React.createElement('div', {
                    className: 'template-category-label'
                },
                    React.createElement('span', {}, 
                        // Remove emoji from the name if present
                        category.name.replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu, '').trim()
                    )
                )
            )
        );
    };

    // Render an expanded category card in the "Show More" modal
    const renderExpandedCategoryCard = (category) => {
        const isSelected = expandedSelectedCategory && expandedSelectedCategory.id === category.id;
        const unsplashUrl = getUnsplashImageUrl(category.id);
        
        return React.createElement('div', {
            key: category.id,
            className: `expanded-category-card relative overflow-hidden rounded-xl transition-all duration-300 cursor-pointer border
                ${isSelected 
                    ? 'border-purple-500 shadow-lg shadow-purple-500/30 scale-[1.02]' 
                    : 'border-transparent hover:border-purple-400 hover:shadow-md hover:shadow-purple-400/20 hover:scale-[1.01]'}`,
            onClick: () => openExpandedCategoryModal(category),
            role: 'button',
            'aria-expanded': isSelected,
            'aria-controls': `expanded-templates-${category.id}`,
            id: `expanded-category-card-${category.id}`,
            tabIndex: 0,
            onKeyDown: (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    openExpandedCategoryModal(category);
                }
            }
        },
            // Card background/image with Unsplash image
            React.createElement('div', {
                className: 'expanded-category-thumbnail relative aspect-video w-full h-full rounded-xl overflow-hidden',
            }, 
                // Background image from Unsplash
                React.createElement('img', {
                    src: unsplashUrl,
                    alt: category.name,
                    className: 'absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110',
                    loading: 'lazy'
                }),
                // Dark overlay gradient
                React.createElement('div', {
                    className: 'absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-black/30'
                }),
                // Centered category name
                React.createElement('div', {
                    className: 'absolute inset-0 flex items-center justify-center p-3',
                },
                    React.createElement('span', {
                        className: 'expanded-category-name font-semibold text-white text-xl tracking-wide text-center px-4 py-2 rounded-lg bg-black/40 backdrop-blur-sm'
                    }, category.name)
                )
            )
        );
    };
    
    // Function to get relevant Unsplash image URLs for each category
    const getUnsplashImageUrl = (categoryId) => {
        const imageMap = {
            // Travel & Vlog - Epic mountain landscape with dramatic sky and vibrant colors
            'travel-vlog': 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?auto=format&fit=crop&w=800&q=80',
            
            // Health Fitness - Energetic fitness scene with dumbbells and vibrant lighting
            'health-fitness': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?auto=format&fit=crop&w=800&q=80',
            
            // Business - Modern skyscraper with dynamic perspective and professional feel
            'business': 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&w=800&q=80',
            
            // Art & Tech - Colorful digital art workspace with creative tools and neon lighting
            'art-tech': 'https://images.unsplash.com/photo-**********-9bc0b252726f?auto=format&fit=crop&w=800&q=80',
            
            // Movies - Dramatic cinema scene with film reels and golden lighting
            'movies': 'https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=800&q=80',
            
            // Tech - Circuit board with vibrant blue and green lighting
            'tech': 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80',
            
            // Reactions - Expressive woman with surprised/excited expression
            'reactions': 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=800&q=80',
            
            // Gaming - Dynamic gaming setup with RGB lighting and controllers
            'gaming': 'https://images.unsplash.com/photo-1542751371-adc38448a05e?auto=format&fit=crop&w=800&q=80',
            
            // Legacy categories (for backward compatibility)
            'education': 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?auto=format&fit=crop&w=800&q=80',
            'vlogging': 'https://images.unsplash.com/photo-1616469829941-c7200edec809?auto=format&fit=crop&w=800&q=80',
            
            // Default fallback - Creative abstract background with vibrant colors
            'default': 'https://images.unsplash.com/photo-1557682224-5b8590cd9ec5?auto=format&fit=crop&w=800&q=80'
        };
        
        return imageMap[categoryId] || imageMap['default'];
    };
    
    // Create an "Add New" card matching template card design
    const renderAddNewCard = () => {
        return React.createElement('div', {
            key: 'add-new',
            className: 'template-category-card add-new-card',
            onClick: () => {
                // Future functionality for adding new templates
                console.log('Add new template functionality coming soon');
            },
            role: 'button',
            id: 'template-add-new',
            tabIndex: 0,
            onKeyDown: (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                    console.log('Add new template functionality coming soon');
                }
            }
        },
            // Card content with consistent structure
            React.createElement('div', {
                className: 'template-category-content',
                style: {
                    // Background matching other cards but with gray tone
                    backgroundColor: 'rgba(161, 161, 170, 0.10)',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative'
                }
            }, 
                // Solar widget-add icon (centered)
                React.createElement('span', {
                    className: 'iconify',
                    'data-icon': 'solar:widget-add-bold-duotone',
                    style: {
                        fontSize: '32px', // Consistent icon size
                        color: 'rgb(113, 113, 122)', // Gray color
                        marginBottom: '8px'
                    }
                }),
                
                // Category label overlay (matching other cards)
                React.createElement('div', {
                    className: 'template-category-label'
                },
                    React.createElement('span', {}, 'More Soon')
                )
            )
        );
    };

    // Render expanded template item card
    const renderExpandedTemplateCard = (template) => {
        return React.createElement('div', {
                    key: template.id,
            className: 'expanded-template-item-card group relative bg-gray-800 border border-gray-700 hover:border-purple-500 rounded-lg overflow-hidden transition-all hover:shadow-md cursor-pointer',
            onClick: () => {
                onTemplateSelect(template);
                closeShowMoreModal();
            },
            role: 'button',
            id: `expanded-template-item-${template.id}`,
            tabIndex: 0,
            onKeyDown: (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    onTemplateSelect(template);
                    closeShowMoreModal();
                }
            }
        },
            // Template thumbnail
                    React.createElement('div', {
                className: `expanded-template-item-thumbnail relative aspect-video bg-gradient-to-br from-${template.templateImagePlaceholder.bgColor.split('-')[1]}-800 to-${template.templateImagePlaceholder.bgColor.split('-')[1]}-600 flex items-center justify-center p-3`
            },
                React.createElement('span', {
                    className: 'expanded-template-item-preview-text text-lg font-bold text-white text-center'
                    }, template.templateImagePlaceholder.text),
                // Apply button overlay on hover
                React.createElement('div', {
                    className: 'expanded-template-item-hover-overlay absolute inset-0 bg-purple-900/70 backdrop-blur-sm flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity'
                },
                    React.createElement('button', {
                        className: 'expanded-template-item-apply-btn px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-full font-medium transform transition hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-purple-500'
                    }, 'Apply Template')
                )
            ),
            // Template info
            React.createElement('div', {
                className: 'expanded-template-item-info p-3 space-y-1'
            },
                React.createElement('h4', {
                    className: 'expanded-template-item-title font-medium text-white text-sm'
                }, template.name),
                React.createElement('p', {
                    className: 'expanded-template-item-description text-xs text-gray-400'
                }, template.description)
            )
        );
    };
    
    // Template Modal Component
    const renderTemplateModal = () => {
        if (!selectedCategory) return null;
        
        return React.createElement('div', {
            className: `template-modal-container fixed inset-0 z-50 flex items-center justify-center p-4 ${modalOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`,
            id: 'template-selection-modal'
        },
            // Backdrop
            React.createElement('div', {
                className: `template-modal-backdrop absolute inset-0 bg-black/70 backdrop-blur-sm transition-opacity ${modalOpen ? 'opacity-100' : 'opacity-0'}`,
                onClick: closeModal
            }),
            // Modal content
            React.createElement('div', {
                className: `template-modal-content w-full max-w-4xl bg-gray-900 rounded-xl border border-gray-700 shadow-xl transform transition-all max-h-[90vh] overflow-hidden flex flex-col ${modalOpen ? 'scale-100' : 'scale-95'}`,
                role: 'dialog',
                'aria-modal': 'true',
                'aria-labelledby': `category-title-${selectedCategory.id}`
            },
                // Header
                React.createElement('div', {
                    className: 'template-modal-header flex items-center justify-between p-4 border-b border-gray-800'
                },
                    // Title
                    React.createElement('h3', {
                        id: `category-title-${selectedCategory.id}`,
                        className: 'template-modal-title text-xl font-semibold text-white flex items-center gap-2'
                    }, 
                        React.createElement('span', {
                            className: `template-modal-icon flex items-center justify-center w-8 h-8 rounded ${selectedCategory.categoryImagePlaceholder.bgColor} text-white font-bold text-base`
                        }, selectedCategory.name.split(' ')[0][0]),
                        selectedCategory.name
                    ),
                    // Close button
                    React.createElement('button', {
                        className: 'template-modal-close-btn w-8 h-8 rounded-full flex items-center justify-center text-gray-400 hover:bg-gray-800 hover:text-white transition-colors',
                        onClick: closeModal,
                        'aria-label': 'Close modal',
                        id: 'template-modal-close-button'
                    },
                        React.createElement('svg', {
                            xmlns: 'http://www.w3.org/2000/svg',
                            viewBox: '0 0 24 24',
                            fill: 'none',
                            stroke: 'currentColor',
                            strokeWidth: '2',
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            className: 'w-5 h-5'
                        },
                            React.createElement('line', { x1: '18', y1: '6', x2: '6', y2: '18' }),
                            React.createElement('line', { x1: '6', y1: '6', x2: '18', y2: '18' })
                        )
                    )
                ),
                // Grid of templates
                React.createElement('div', {
                    className: 'template-modal-body overflow-y-auto p-4 flex-grow'
                },
                    React.createElement('div', {
                        className: 'template-items-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'
                    },
                        selectedCategory.templates.map(template => 
                            React.createElement('div', {
                                key: template.id,
                                className: 'template-item-card group relative bg-gray-800 border border-gray-700 hover:border-purple-500 rounded-lg overflow-hidden transition-all hover:shadow-md cursor-pointer',
                                onClick: () => {
                                    onTemplateSelect(template);
                                    closeModal();
                                },
                                role: 'button',
                                id: `template-item-${template.id}`,
                                tabIndex: 0,
                                onKeyDown: (e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                        e.preventDefault();
                                        onTemplateSelect(template);
                                        closeModal();
                                    }
                                }
                            },
                                // Template thumbnail
                                React.createElement('div', {
                                    className: `template-item-thumbnail relative aspect-video bg-gradient-to-br from-${template.templateImagePlaceholder.bgColor.split('-')[1]}-800 to-${template.templateImagePlaceholder.bgColor.split('-')[1]}-600 flex items-center justify-center p-3`
                                },
                                    React.createElement('span', {
                                        className: 'template-item-preview-text text-lg font-bold text-white text-center'
                                    }, template.templateImagePlaceholder.text),
                                    // Apply button overlay on hover
                                    React.createElement('div', {
                                        className: 'template-item-hover-overlay absolute inset-0 bg-purple-900/70 backdrop-blur-sm flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity'
                                    },
                                        React.createElement('button', {
                                            className: 'template-item-apply-btn px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-full font-medium transform transition hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-purple-500'
                                        }, 'Apply Template')
                                    )
                                ),
                                // Template info
                                React.createElement('div', {
                                    className: 'template-item-info p-3 space-y-1'
                                },
                                    React.createElement('h4', {
                                        className: 'template-item-title font-medium text-white text-sm'
                                    }, template.name),
                                    React.createElement('p', {
                                        className: 'template-item-description text-xs text-gray-400'
                                    }, template.description)
                                )
                            )
                        )
                    )
                ),
                // Footer
                React.createElement('div', {
                    className: 'template-modal-footer p-4 border-t border-gray-800 flex justify-end'
                },
                    React.createElement('button', {
                        className: 'template-modal-close-button px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-md font-medium',
                        onClick: closeModal,
                        id: 'template-modal-footer-close-btn'
                    }, 'Close')
                )
            )
        );
    };

    // Show More Modal Component
    const renderShowMoreModal = () => {
        return React.createElement('div', {
            className: `show-more-modal-container fixed inset-0 z-50 flex items-center justify-center p-4 ${showMoreModalOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`,
            id: 'show-more-selection-modal'
        },
            // Backdrop
            React.createElement('div', {
                className: `show-more-modal-backdrop absolute inset-0 bg-black/70 backdrop-blur-sm transition-opacity ${showMoreModalOpen ? 'opacity-100' : 'opacity-0'}`,
                onClick: closeShowMoreModal
            }),
            // Modal content
            React.createElement('div', {
                className: `show-more-modal-content w-full max-w-6xl bg-gray-900 rounded-xl border border-gray-700 shadow-xl transform transition-all max-h-[90vh] overflow-hidden flex flex-col ${showMoreModalOpen ? 'scale-100' : 'scale-95'}`,
                role: 'dialog',
                'aria-modal': 'true',
                'aria-labelledby': 'show-more-title'
            },
                // Header
                React.createElement('div', {
                    className: 'show-more-modal-header flex items-center justify-between p-4 border-b border-gray-800'
                },
                    // Title
                    React.createElement('h3', {
                        id: 'show-more-title',
                        className: 'show-more-modal-title text-xl font-semibold text-white flex items-center gap-2'
                    }, 
                        React.createElement('span', {
                            className: 'show-more-modal-icon flex items-center justify-center w-8 h-8 rounded bg-purple-600 text-white font-bold text-base'
                        }, '+'),
                        'More Template Categories'
                    ),
                    // Close button
                    React.createElement('button', {
                        className: 'show-more-modal-close-btn w-8 h-8 rounded-full flex items-center justify-center text-gray-400 hover:bg-gray-800 hover:text-white transition-colors',
                        onClick: closeShowMoreModal,
                        'aria-label': 'Close modal',
                        id: 'show-more-modal-close-button'
                    },
                        React.createElement('svg', {
                            xmlns: 'http://www.w3.org/2000/svg',
                            viewBox: '0 0 24 24',
                            fill: 'none',
                            stroke: 'currentColor',
                            strokeWidth: '2',
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            className: 'w-5 h-5'
                        },
                            React.createElement('line', { x1: '18', y1: '6', x2: '6', y2: '18' }),
                            React.createElement('line', { x1: '6', y1: '6', x2: '18', y2: '18' })
                        )
                    )
                ),
                
                // Content section - split into two columns on larger screens
                React.createElement('div', {
                    className: 'show-more-modal-body overflow-y-auto p-4 flex-grow flex flex-col lg:flex-row gap-4'
                },
                    // Categories column (left)
                    React.createElement('div', {
                        className: 'categories-column lg:w-1/3'
                    },
                        React.createElement('h4', {
                            className: 'text-sm font-medium text-gray-400 mb-4'
                        }, 'Browse Categories'),
                        
                        // Grid of expanded categories
                        React.createElement('div', {
                            className: 'expanded-categories-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-4'
                        }, expandedTemplatesData.map(renderExpandedCategoryCard))
                    ),
                    
                    // Templates column (right)
                    React.createElement('div', {
                        className: 'templates-column lg:w-2/3 bg-gray-800/50 rounded-xl p-4'
                    },
                        // Selected category details
                        expandedSelectedCategory ? React.createElement('div', { className: 'selected-category-details mb-4' },
                            React.createElement('h3', {
                                className: 'text-xl font-semibold text-white'
                            }, expandedSelectedCategory.name),
                            React.createElement('p', {
                                className: 'text-sm text-gray-400'
                            }, expandedSelectedCategory.description)
                        ) : React.createElement('div', { className: 'no-selection-message flex flex-col items-center justify-center p-6 h-full' },
                            React.createElement('p', {
                                className: 'text-lg text-gray-500 text-center'
                            }, 'Select a category to explore templates')
                        ),
                        
                        // Template grid (if a category is selected)
                        expandedSelectedCategory && React.createElement('div', {
                            className: 'expanded-templates-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'
                        }, expandedSelectedCategory.templates.map(renderExpandedTemplateCard))
                    )
                ),
                
                // Footer
                React.createElement('div', {
                    className: 'show-more-modal-footer p-4 border-t border-gray-800 flex justify-end'
                },
                    React.createElement('button', {
                        className: 'show-more-modal-close-button px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-md font-medium',
                        onClick: closeShowMoreModal,
                        id: 'show-more-modal-footer-close-btn'
                    }, 'Close')
                )
            )
        );
    };
    
    // Main render method - render categories in a grid with max 3 visible + show more
    return React.createElement('section', {
        className: 'premade-templates-section mt-6 mb-8',
        'aria-labelledby': 'premade-templates-heading'
    },
        // Section heading with info icon
        React.createElement('div', {
            className: 'flex items-center gap-2 mb-4'
        },
            React.createElement('h2', {
                id: 'premade-templates-heading',
                className: 'text-xl font-semibold text-white flex items-center gap-2'
            }, 
                React.createElement('svg', {
                    xmlns: 'http://www.w3.org/2000/svg',
                    fill: 'none',
                    viewBox: '0 0 24 24',
                    stroke: 'currentColor',
                    className: 'w-5 h-5 text-purple-400'
                },
                    React.createElement('path', {
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        strokeWidth: 2,
                        d: 'M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z'
                    })
                ),
                'Premade Templates'
            ),
            // Info icon with tooltip
            React.createElement('div', {
                className: 'relative group',
                tabIndex: 0,
                'aria-label': 'Information about premade templates',
                role: 'button'
            },
                React.createElement('svg', {
                    xmlns: 'http://www.w3.org/2000/svg',
                    fill: 'none',
                    viewBox: '0 0 24 24',
                    stroke: 'currentColor',
                    className: 'w-5 h-5 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                },
                    React.createElement('path', {
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        strokeWidth: 2,
                        d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                    })
                ),
                // Tooltip
                React.createElement('div', {
                    className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                    role: 'tooltip',
                    'aria-hidden': 'true'
                },
                    React.createElement('div', {
                        className: 'flex items-start gap-2'
                    },
                        React.createElement('svg', {
                            xmlns: 'http://www.w3.org/2000/svg',
                            fill: 'none',
                            viewBox: '0 0 24 24',
                            stroke: 'currentColor',
                            className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                strokeWidth: 2,
                                d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                            })
                        ),
                        React.createElement('p', null, 'These templates provide inspiration and starting points for your YouTube thumbnails. Select one to quickly generate a professional-looking design.')
                    ),
                    // Triangle pointer
                    React.createElement('div', {
                        className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                    })
                )
            )
        ),
        
        // Grid of categories - 3x3 layout matching Figma design
        React.createElement('div', {
            className: 'templates-grid grid grid-cols-3 gap-2 w-full max-w-[324px]'
        },
            // Display all 5 categories
            templatesData.map(category => renderCategoryCard(category)),
            
            // Add New card as the 6th item
            renderAddNewCard()
        ),
        
        // Modal for displaying templates within a category
        renderTemplateModal(),
        
        // Show More modal for displaying expanded template categories
        renderShowMoreModal()
    );
};

// ================= New Inline Component: BackgroundStyleSelector =================
const BackgroundStyleSelector = ({
    selectedStyleId,
    onSelectStyle,
    selectedSolidColor,
    onSolidColorChange
}) => {
    // Show categories as tabs across the top
    const categories = TEMPLATE_BG_CATEGORIES;
    const initialCat = categories[0]?.id || 'solid';
    const [activeCat, setActiveCat] = React.useState(initialCat);
    const [isModalOpen, setIsModalOpen] = React.useState(false);
    const [selectedCategory, setSelectedCategory] = React.useState(null);

    // Filter styles for the active category
    const stylesForCat = React.useMemo(
        () => BACKGROUND_STYLES_DATA.filter((s) => s.category === activeCat),
        [activeCat]
    );

    // Handle category selection
    const handleCategoryClick = (category) => {
        setActiveCat(category.id);
        if (category.id === 'solid') {
            // For solid colors, just show the color options (no modal)
            setSelectedCategory(null);
            setIsModalOpen(false);
        } else {
            // For other categories, open the modal
            setSelectedCategory(category);
            setIsModalOpen(true);
        }
    };

    // Close modal
    const handleCloseModal = () => {
        setIsModalOpen(false);
    };

    // Renders one style button (solid color swatch or image thumb)
    const renderStyleButton = (style) => {
        const isSelected = selectedStyleId === style.id;
        const commonBtnCls = `relative flex items-center justify-center p-2 rounded-lg border-2 cursor-pointer transition-all focus:outline-none focus:ring-2 focus:ring-purple-500 ${isSelected ? 'border-purple-500' : 'border-gray-600 hover:border-purple-400'}`;

        // Solid color swatch
        if (style.type === 'solid') {
            return React.createElement('button', {
                key: style.id,
                onClick: () => onSelectStyle(style.id, 'solid', style.color),
                className: `${commonBtnCls}`,
                title: style.name,
                'aria-pressed': isSelected
            },
                React.createElement('div', {
                    className: 'w-16 h-10 rounded',
                    style: { backgroundColor: style.color }
                })
            );
        }
        // Template thumbnail (placeholder colored box if preview not available)
        return React.createElement('button', {
            key: style.id,
            onClick: () => onSelectStyle(style.id, 'template'),
            className: `${commonBtnCls} w-16 h-10 bg-gray-700 text-[10px] text-gray-300`,
            title: style.name,
            'aria-pressed': isSelected
        }, style.name);
    };

    // Render category tile
    const renderCategoryTile = (category) => {
        const isActive = activeCat === category.id;
        
        return React.createElement('div', {
            key: category.id,
            onClick: () => handleCategoryClick(category),
            className: `background-category-card flex flex-col items-center justify-center p-3 rounded-lg cursor-pointer transition-all 
                ${isActive ? 'bg-purple-900/30 border-2 border-purple-500' : 'bg-gray-700/50 border border-gray-700 hover:border-purple-400'} 
                h-24 w-full focus:outline-none focus:ring-2 focus:ring-purple-500`,
            id: `background-category-${category.id}`,
            'aria-pressed': isActive,
            role: 'button',
            tabIndex: '0',
            'aria-label': `${category.name} background style`
        },
            React.createElement('span', { 
                className: `iconify text-2xl mb-1 ${isActive ? 'text-purple-300' : 'text-gray-300'}`, 
                'data-icon': category.icon || 'solar:palette-bold-duotone'
            }),
            React.createElement('span', { 
                className: `text-sm font-medium ${isActive ? 'text-purple-200' : 'text-gray-300'}`
            }, category.name)
        );
    };

    // Solid color custom picker (visible only on solid tab)
    const solidColorPicker = activeCat === 'solid'
        ? React.createElement('div', { 
            className: 'mt-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700 background-solid-color-picker'
          },
            React.createElement('div', { 
                className: 'flex items-center justify-between mb-2' 
            },
                React.createElement('span', { 
                    className: 'text-sm font-medium text-gray-300'
                }, 'Choose a solid color:'),
                React.createElement('span', { 
                    className: 'text-xs text-gray-400 uppercase'
                }, selectedSolidColor)
            ),
            React.createElement('div', { 
                className: 'grid grid-cols-3 sm:grid-cols-3 md:grid-cols-6 gap-2 mb-3' 
            },
                ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF', '#FFFFFF', '#000000', '#800080'].map(color => 
                    React.createElement('button', {
                        key: color,
                        onClick: () => onSolidColorChange(color),
                        className: `w-full aspect-square rounded-md border-2 ${selectedSolidColor === color ? 'border-purple-500' : 'border-gray-700'} hover:border-purple-400 transition-all`,
                        style: { backgroundColor: color },
                        'aria-label': `Select color ${color}`,
                        'aria-pressed': selectedSolidColor === color
                    })
                )
            ),
            React.createElement('div', { 
                className: 'flex items-center gap-3' 
            },
                React.createElement('label', { 
                    htmlFor: 'customColorPicker',
                    className: 'text-xs font-medium text-gray-400'
                }, 'Custom:'),
                React.createElement('input', {
                    id: 'customColorPicker',
                    type: 'color',
                    value: selectedSolidColor,
                    onChange: (e) => onSolidColorChange(e.target.value),
                    className: 'h-8 w-14 border-0 p-0 bg-transparent cursor-pointer'
                })
            )
        )
        : null;

    return React.createElement('div', { 
        className: 'background-style-selector flex flex-col gap-3',
        id: 'background-styles-section'
    },
        // Header text
        React.createElement('p', { 
            className: 'text-sm text-gray-400 mb-1'
        }, 'Select a background style category:'),
        
        // Category grid
        React.createElement('div', { 
            className: 'background-categories-grid grid grid-cols-3 gap-2',
            style: { width: 'calc(100% - 8px)' }
        }, 
            categories.map(category => renderCategoryTile(category))
        ),
        
        // Solid color picker (only visible when "Solid Color" category is active)
        solidColorPicker,
        
        // Background template modal (for non-solid categories)
        isModalOpen && selectedCategory && React.createElement(BackgroundTemplateModal, {
            category: selectedCategory,
            onClose: handleCloseModal,
            onSelectStyle: onSelectStyle,
            selectedStyleId: selectedStyleId
        })
    );
};
// ================= End BackgroundStyleSelector =================

// ================= Main App Component =================
console.log("App.jsx script started"); // Check if script loads

// Add this before the App component (around line 1525)
const ResetButton = ({ onClick }) => {
    return React.createElement('button', {
        onClick: onClick,
        className: 'reset-button flex items-center gap-2 px-3 py-1.5 bg-transparent  rounded-lg text-sm font-medium transition-all duration-200 hover:bg-gray-700/30 hover:text-gray-300 hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900',
        'aria-label': 'Reset all settings'
    }, 
        React.createElement('span', {
            className: 'iconify',
            'data-icon': 'solar:refresh-bold-duotone',
            style: { width: '16px', height: '16px', color: '#F9C97C' }
        }),
        'Reset'
    );
};

// Fixed positioning tooltip system helper - import from ControlPanel if needed
const createFixedTooltip = (tooltipText, tooltipId = '') => {
    const iconRef = React.useRef(null);
    const tooltipRef = React.useRef(null);
    const [isVisible, setIsVisible] = React.useState(false);
    const [position, setPosition] = React.useState({ top: 0, left: 0, placement: 'top' });

    const calculatePosition = React.useCallback(() => {
        if (!iconRef.current || !tooltipRef.current) return;

        const iconRect = iconRef.current.getBoundingClientRect();
        const tooltipRect = tooltipRef.current.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        const padding = 12;
        let newPosition = { top: 0, left: 0, placement: 'top' };

        // Try positioning above first
        if (iconRect.top - tooltipRect.height - padding >= 0) {
            newPosition.top = iconRect.top - tooltipRect.height - padding;
            newPosition.left = iconRect.left + (iconRect.width / 2) - (tooltipRect.width / 2);
            newPosition.placement = 'top';
        }
        // If not enough space above, position below
        else if (iconRect.bottom + tooltipRect.height + padding <= viewportHeight) {
            newPosition.top = iconRect.bottom + padding;
            newPosition.left = iconRect.left + (iconRect.width / 2) - (tooltipRect.width / 2);
            newPosition.placement = 'bottom';
        }
        // Position to the right if needed
        else if (iconRect.right + tooltipRect.width + padding <= viewportWidth) {
            newPosition.top = iconRect.top + (iconRect.height / 2) - (tooltipRect.height / 2);
            newPosition.left = iconRect.right + padding;
            newPosition.placement = 'right';
        }
        // Last resort: position to the left
        else {
            newPosition.top = iconRect.top + (iconRect.height / 2) - (tooltipRect.height / 2);
            newPosition.left = iconRect.left - tooltipRect.width - padding;
            newPosition.placement = 'left';
        }

        // Viewport boundary checks
        if (newPosition.left < padding) {
            newPosition.left = padding;
        } else if (newPosition.left + tooltipRect.width > viewportWidth - padding) {
            newPosition.left = viewportWidth - tooltipRect.width - padding;
        }

        if (newPosition.top < padding) {
            newPosition.top = padding;
        } else if (newPosition.top + tooltipRect.height > viewportHeight - padding) {
            newPosition.top = viewportHeight - tooltipRect.height - padding;
        }

        setPosition(newPosition);
    }, []);

    const handleMouseEnter = () => {
        setIsVisible(true);
        requestAnimationFrame(() => {
            calculatePosition();
        });
    };

    const handleMouseLeave = () => {
        setIsVisible(false);
    };

    const handleFocus = () => {
        setIsVisible(true);
        requestAnimationFrame(() => {
            calculatePosition();
        });
    };

    const handleBlur = () => {
        setIsVisible(false);
    };

    React.useEffect(() => {
        const handleUpdate = () => {
            if (isVisible) {
                calculatePosition();
            }
        };

        window.addEventListener('scroll', handleUpdate, true);
        window.addEventListener('resize', handleUpdate);

        return () => {
            window.removeEventListener('scroll', handleUpdate, true);
            window.removeEventListener('resize', handleUpdate);
        };
    }, [isVisible, calculatePosition]);

    const getArrowStyles = () => {
        const arrowSize = 8;
        const arrowStyles = {
            position: 'absolute',
            width: `${arrowSize}px`,
            height: `${arrowSize}px`,
            background: '#1F2937', // Match tooltip background
            transform: 'rotate(45deg)',
            zIndex: -1
        };

        switch (position.placement) {
            case 'top':
                return { ...arrowStyles, bottom: `-${arrowSize / 2}px`, left: '50%', marginLeft: `-${arrowSize / 2}px` };
            case 'bottom':
                return { ...arrowStyles, top: `-${arrowSize / 2}px`, left: '50%', marginLeft: `-${arrowSize / 2}px` };
            case 'right':
                return { ...arrowStyles, left: `-${arrowSize / 2}px`, top: '50%', marginTop: `-${arrowSize / 2}px` };
            case 'left':
                return { ...arrowStyles, right: `-${arrowSize / 2}px`, top: '50%', marginTop: `-${arrowSize / 2}px` };
            default:
                return arrowStyles;
        }
    };

    return React.createElement('div', {
        className: 'tooltip-icon-wrapper relative inline-block',
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
        onFocus: handleFocus,
        onBlur: handleBlur,
        tabIndex: 0,
        'aria-label': 'Information about this setting',
        role: 'button'
    },
        React.createElement('span', {
            ref: iconRef,
            className: 'tooltip-icon w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help',
            role: 'img',
            'aria-label': 'Information about this setting',
            dangerouslySetInnerHTML: {
                __html: `<span class="iconify" data-icon="solar:info-circle-linear" style="color: currentColor;"></span>`
            }
        }),
        // Fixed position tooltip
        isVisible && React.createElement('div', {
            ref: tooltipRef,
            className: `tooltip-fixed fixed bg-gray-800 text-gray-100 text-xs rounded-lg shadow-xl p-3 transition-opacity duration-200 pointer-events-none`,
            style: {
                top: `${position.top}px`,
                left: `${position.left}px`,
                zIndex: 9999,
                maxWidth: '320px',
                opacity: isVisible ? 1 : 0,
                visibility: isVisible ? 'visible' : 'hidden'
            },
            role: 'tooltip',
            'aria-hidden': !isVisible,
            id: tooltipId
        },
            React.createElement('div', { className: 'relative z-10' }, tooltipText),
            React.createElement('div', { style: getArrowStyles() })
        )
    );
};

export const App = () => {
    // ================= State Management =================
    const [userPrompt, setUserPrompt] = React.useState('');
    const [imageURL, setImageURL] = React.useState('');
    const [includePerson, setIncludePerson] = React.useState(true);
    const [includeIcons, setIncludeIcons] = React.useState(false);
    const [textOverlay, setTextOverlay] = React.useState(true);
    const [overlayText, setOverlayText] = React.useState('');
    const [isEditingOverlayText, setIsEditingOverlayText] = React.useState(false);
    const [textPosition, setTextPosition] = React.useState('top-right');
    const [selectedTextSize, setSelectedTextSize] = React.useState('medium');
    const [selectedFontFamily, setSelectedFontFamily] = React.useState('Inter');
    const [selectedExpression, setSelectedExpression] = React.useState('excited');
    const [selectedGender, setSelectedGender] = React.useState('any');
    const [primaryTextColor, setPrimaryTextColor] = React.useState('#FFD700');
    const [secondaryTextColor, setSecondaryTextColor] = React.useState('#FFFFFF');
    const [selectedPalette, setSelectedPalette] = React.useState(null);
    const [isLoading, setIsLoading] = React.useState(false);
    const [fitFullCanvas, setFitFullCanvas] = React.useState(false);
    const [showLayoutSimulator, setShowLayoutSimulator] = React.useState(false);
    const [showSafeZone, setShowSafeZone] = React.useState(false);
    const [errorMsg, setErrorMsg] = React.useState('');
    const [customFaceImageUrl, setCustomFaceImageUrl] = React.useState('');
    const [imageSourceType, setImageSourceType] = React.useState('ai');
    const [backgroundCustomizationEnabled, setBackgroundCustomizationEnabled] = React.useState(false);
    const [selectedBackgroundType, setSelectedBackgroundType] = React.useState('smart');
    const [selectedBackgroundStyleId, setSelectedBackgroundStyleId] = React.useState('default');
    const [selectedSolidBgColor, setSelectedSolidBgColor] = React.useState('#6366f1');
    const [activeTab, setActiveTab] = React.useState('controls');
    const [activeTemplateCategory, setActiveTemplateCategory] = React.useState(null);
    const [isLowCostMode, setIsLowCostMode] = React.useState(false);
    const [selectedQuality, setSelectedQuality] = React.useState('normal');
    const [isTyping, setIsTyping] = React.useState(false);
    const [cursorVisible, setCursorVisible] = React.useState(false);
    const [isTemplateActive, setIsTemplateActive] = React.useState(false);
    
    // NEW: State for collapsible sections
    const [isBGStylesOpen, setIsBGStylesOpen] = React.useState(false);

    // Add refs for the quality selector
    const qualityContainerRef = React.useRef(null);
    const qualityPillRef = React.useRef(null);

    // ================= Template Data =================
    const templatesData = [
        {
            id: 'gaming',
            name: 'Gaming',
            templates: []
        },
        {
            id: 'tech',
            name: 'Tech Reviews',
            templates: []
        },
        {
            id: 'reaction',
            name: 'Reaction Videos',
            templates: []
        }
    ];

    const expandedTemplatesData = [
        {
            id: 'movies',
            name: 'Movies & Entertainment',
            templates: []
        },
        {
            id: 'vlogging',
            name: 'Vlogging',
            templates: []
        }
    ];

    // Combine both template arrays
    const premadeTemplatesData = [...templatesData, ...expandedTemplatesData];

    // ================================================
    // Background Style Handlers (moved from ControlPanel)
    // ================================================
    const handleBackgroundStyleSelect = (styleId, styleType, initialColor) => {
        setSelectedBackgroundStyleId(styleId);
        setSelectedBackgroundType(styleType);
        if (styleType === 'solid' && initialColor) {
            setSelectedSolidBgColor(initialColor);
        }
    };

    const handleSolidBgColorChange = (color) => {
        setSelectedSolidBgColor(color);
    };

    const handleTabClick = (tabId) => {
        setActiveTab(tabId);
    };

    const handleToggleSection = (sectionName, isOpen) => {
        // Handler for section toggle events
        console.log(`Section "${sectionName}" is now ${isOpen ? 'open' : 'closed'}`);
    };

    const clearError = () => {
        setErrorMsg('');
    };

    const handleReset = () => {
        // Reset state function
        setUserPrompt('');
        setImageURL('');
        setIncludePerson(true);
        setIncludeIcons(false);
        setTextOverlay(true);
        setOverlayText('');
        setIsEditingOverlayText(false);
        setTextPosition('top-right');
        setSelectedTextSize('medium');
        setSelectedFontFamily('Inter');
        setSelectedExpression('excited');
        setSelectedGender('any');
        setPrimaryTextColor('#FFD700');
        setSecondaryTextColor('#FFFFFF');
        setSelectedPalette(null);
        setIsLoading(false);
        setFitFullCanvas(false);
        setShowLayoutSimulator(false);
        setShowSafeZone(false);
        setErrorMsg('');
        setCustomFaceImageUrl('');
        setImageSourceType('ai');
        setBackgroundCustomizationEnabled(false);
        setSelectedBackgroundType('smart');
        setSelectedBackgroundStyleId('default');
        setSelectedSolidBgColor('#6366f1');
        setIsLowCostMode(false);
        setSelectedQuality('normal');
        setIsTemplateActive(false);
        console.log('Reset all settings to default');
    };

    const handleImprovePrompt = async () => {
        // This function would call the OpenAI API to improve the prompt
        // Placeholder for now
        console.log('Improving prompt...');
        const improved = userPrompt + ' (enhanced)';
        setUserPrompt(improved);
    };

    const handlePromptChange = (event) => {
        setUserPrompt(event.target.value);
        
        // Handle typing indicators
        setIsTyping(true);
        setTimeout(() => setIsTyping(false), 500);
    };

    const handleImageSourceTypeChange = (newSourceType) => {
        setImageSourceType(newSourceType);
        // Clear any existing custom face image when switching types
        if (newSourceType === 'ai') {
            setCustomFaceImageUrl('');
        }
    };

    const handleFileUpload = (event) => {
        const file = event.target.files[0];
        if (!file) return;
        
        // Validate file type
        const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (!validTypes.includes(file.type)) {
            setErrorMsg('Invalid file type. Please upload JPEG, PNG, or WebP images only.');
            return;
        }

        // Validate file size (2MB limit)
        const maxSizeInBytes = 2 * 1024 * 1024;
        if (file.size > maxSizeInBytes) {
            setErrorMsg('File too large. Maximum size is 2MB.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const result = e.target.result;
            setCustomFaceImageUrl(result);
            setImageSourceType('upload'); // Switch to upload mode
            clearError();
        };
        reader.onerror = () => {
            setErrorMsg('Error reading file. Please try again.');
        };
        reader.readAsDataURL(file);
    };

    const handleToggleChange = (setter, isPersonToggle = false) => {
        return (checked) => {
            setter(checked);
            
            // Special logic for person toggle
            if (isPersonToggle) {
                // If turning off person, also turn off facial expressions
                if (!checked) {
                    setSelectedExpression('excited'); // Reset to default
                }
            }
        };
    };

    const handleTemplateSelect = (template) => {
        // Apply template settings
        setUserPrompt(template.prompt || '');
        setIncludePerson(template.includePerson !== undefined ? template.includePerson : true);
        setIncludeIcons(template.includeIcons !== undefined ? template.includeIcons : false);
        setTextOverlay(template.textOverlay !== undefined ? template.textOverlay : true);
        setSelectedExpression(template.expression || 'excited');
        
        // Set template colors if provided
        if (template.primaryColor) {
            setPrimaryTextColor(template.primaryColor);
        }
        if (template.secondaryColor) {
            setSecondaryTextColor(template.secondaryColor);
        }
        
        // Set overlay text if provided
        if (template.overlayText) {
            setOverlayText(template.overlayText);
        }
        
        // Mark that a template is active
        setIsTemplateActive(true);
        
        console.log('Applied template:', template);
    };

    const handleQualityChange = (newQuality) => {
        setSelectedQuality(newQuality);
    };

    const handleQualityToggle = () => {
        setIsLowCostMode(!isLowCostMode);
    };

    const buildFullPrompt = () => {
        return formatPrompt({
            userPrompt,
            includePerson,
            includeIcons,
            textOverlay,
            overlayText,
            selectedExpression,
            selectedGender,
            primaryTextColor,
            secondaryTextColor,
            selectedFontFamily,
            selectedTextSize,
            textPosition,
            selectedBackgroundType,
            selectedBackgroundStyleId,
            selectedSolidBgColor
        });
    };

    const handleGenerateClick = async () => {
        if (!userPrompt.trim()) {
            setErrorMsg('Please enter a prompt first.');
            return;
        }

        setIsLoading(true);
        setErrorMsg('');

        try {
            const fullPrompt = buildFullPrompt();
            console.log('Generated prompt:', fullPrompt);

            // Here you would call your image generation API
            // For now, we'll use a placeholder
            
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // For demo purposes, set a placeholder image
            const placeholderImage = `https://via.placeholder.com/1280x720/1f1f1f/ffffff?text=${encodeURIComponent('Generated+Thumbnail')}`;
            setImageURL(placeholderImage);
            
        } catch (error) {
            console.error('Error generating image:', error);
            setErrorMsg('Failed to generate image. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    // ================= Effects =================
    React.useEffect(() => {
        // Clear error after 5 seconds
        if (errorMsg) {
            const timer = setTimeout(() => {
                setErrorMsg('');
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [errorMsg]);

    // Effect to handle quality selector positioning
    React.useEffect(() => {
        if (qualityPillRef.current && qualityContainerRef.current) {
            const container = qualityContainerRef.current;
            const pill = qualityPillRef.current;
            const containerWidth = container.offsetWidth;
            const buttonWidth = containerWidth / 3;
            
            let position = 0;
            if (selectedQuality === 'normal') {
                position = buttonWidth;
            } else if (selectedQuality === 'hd') {
                position = buttonWidth * 2;
            }
            
            // Apply the position with perfect centering
            pill.style.transform = `translateX(${position}px)`;
        }
    }, [selectedQuality]);

    // Handle browser history
    React.useEffect(() => {
        const handleLocationChange = () => {
            // This would be used for route handling if needed
            console.log('Location changed');
        };

        // Set up any location change listeners here
        window.addEventListener('popstate', handleLocationChange);
        
        return () => {
            window.removeEventListener('popstate', handleLocationChange);
        };
    }, []);

    // ================= Render =================
    return React.createElement('div', { 
        className: 'three-panel-layout min-h-screen bg-gray-900 text-white overflow-hidden',
        id: 'main-app-container'
    },
        // === Left Sidebar (Design Controls) ===
        React.createElement('div', { 
            className: 'left-sidebar border-r border-gray-700',
            id: 'left-sidebar-controls'
        },
            // Tab Navigation
            React.createElement('div', { 
                className: 'sidebar-tabs',
                id: 'sidebar-tabs-nav'
            },
                React.createElement('div', { 
                    className: 'tabs-container border-b border-gray-700'
                },
                    React.createElement('button', {
                        id: 'controls-tab-button',
                        className: `tab-button ${activeTab === 'controls' ? 'active' : ''}`,
                        onClick: () => handleTabClick('controls'),
                        role: 'tab',
                        'aria-selected': activeTab === 'controls',
                        'aria-controls': 'controls-tab-content'
                    }, 'Design Controls'),
                    React.createElement('button', {
                        id: 'templates-tab-button',
                        className: `tab-button ${activeTab === 'templates' ? 'active' : ''}`,
                        onClick: () => handleTabClick('templates'),
                        role: 'tab',
                        'aria-selected': activeTab === 'templates',
                        'aria-controls': 'templates-tab-content'
                    }, 'Templates')
                )
            ),
            
            // Tab Content
            React.createElement('div', { className: 'tab-content-container' },
                activeTab === 'controls' && React.createElement('div', {
                    id: 'controls-tab-content',
                    role: 'tabpanel',
                    'aria-labelledby': 'controls-tab-button'
                },
                    React.createElement(CollapsibleControlPanel, {
                        includePerson,
                        includeIcons,
                        textOverlay,
                        selectedExpression,
                        handleToggleChange,
                        setIncludePerson,
                        setIncludeIcons,
                        setTextOverlay,
                        fitFullCanvas,
                        setFitFullCanvas,
                        showLayoutSimulator,
                        setShowLayoutSimulator,
                        showSafeZone,
                        setShowSafeZone,
                        overlayText,
                        setOverlayText,
                        isEditingOverlayText,
                        setIsEditingOverlayText,
                        textPosition,
                        setTextPosition,
                        selectedTextSize,
                        setSelectedTextSize,
                        selectedFontFamily,
                        setSelectedFontFamily,
                        selectedGender,
                        setSelectedGender,
                        primaryTextColor,
                        setPrimaryTextColor,
                        secondaryTextColor,
                        setSecondaryTextColor,
                        selectedPalette,
                        setSelectedPalette,
                        setSelectedExpression,
                        customFaceImageUrl,
                        setCustomFaceImageUrl,
                        imageSourceType,
                        handleImageSourceTypeChange,
                        handleFileUpload,
                        setErrorMsg,
                            backgroundCustomizationEnabled, // Pass this prop
                            setBackgroundCustomizationEnabled, // And its setter
                        selectedBackgroundType,
                        selectedBackgroundStyleId,
                        handleBackgroundStyleSelect,
                        selectedSolidBgColor,
                        handleSolidBgColorChange,
                            isBGStylesOpen, // Pass state for Background Styles
                            setIsBGStylesOpen // Pass setter for Background Styles
                    })
                ),
                
                    activeTab === 'templates' && React.createElement('div', {
                        id: 'templates-tab-content',
                        role: 'tabpanel',
                        'aria-labelledby': 'templates-tab-button'
                    },
                        React.createElement(PremadeTemplatesSection, {
                            templatesData: premadeTemplatesData, // Pass the regular templates
                            expandedTemplatesData: expandedTemplatesData, // Pass the expanded templates
                            onTemplateSelect: handleTemplateSelect,
                            activeCategory: activeTemplateCategory,
                            setActiveCategory: setActiveTemplateCategory,
                        })
                    )
                ),
                // === Center Panel (Prompt & Actions) ===
                React.createElement('div', { 
                    className: 'main-center-panel flex flex-col gap-4 p-6 overflow-y-auto',
                    id: 'center-panel-main'
                },
                    // Preview Workspace Section
                    React.createElement('div', {
                        className: 'preview-workspace-section flex-grow',
                    id: 'preview-workspace'
                },
                        // Header with title and reset button
                    React.createElement('div', { 
                            className: 'workspace-header flex items-center justify-between mb-4'
                    },
                        React.createElement('h2', { 
                                className: 'text-xl font-semibold text-white'
                        }, 'Preview Workspace'),
                        React.createElement(ResetButton, { onClick: handleReset })
                    ),
                        // Preview container
                        React.createElement('div', {
                            className: 'preview-wrapper',
                            id: 'thumbnail-preview-wrapper'
                        },
                        React.createElement(ThumbnailPreview, { 
                            imageURL: imageURL, 
                            isLoading: isLoading, 
                            showLayoutSimulator: showLayoutSimulator, 
                            showSafeZone: showSafeZone, 
                            includePerson: includePerson, 
                            includeIcons: includeIcons, 
                            textOverlay: textOverlay,
                            overlayText: overlayText,
                            selectedFontFamily: selectedFontFamily,
                            selectedTextSize: selectedTextSize,
                            primaryTextColor: primaryTextColor,
                            secondaryTextColor: secondaryTextColor
                        })
                    )
                ),
                
                    // Prompt and Action Controls Section
                React.createElement('div', { 
                        className: 'prompt-controls-section mt-auto',
                        id: 'prompt-controls'
                },
                        React.createElement(PromptInput, { 
                            displayValue: userPrompt,
                            onChange: handlePromptChange, 
                            isLocked: isTemplateActive, 
                            onImprovePrompt: handleImprovePrompt, 
                            isTyping: isTyping, 
                            cursorVisible: cursorVisible 
                        }),
                        // Quality selector and action buttons row
                        React.createElement('div', {
                            className: 'controls-bottom-row flex items-center justify-between gap-4 mt-4'
                        },
                            // Quality selector with JavaScript-based positioning
                            React.createElement('div', { 
                                className: 'quality-selector flex items-center gap-2'
                            },
                                React.createElement('span', { 
                                    className: 'text-sm text-gray-400'
                                }, 'Quality:'),
                                React.createElement('div', {
                                    ref: qualityContainerRef,
                                    className: 'quality-options relative rounded-xl overflow-hidden',
                                    style: {
                                        backgroundColor: 'rgb(63, 63, 70)',
                                        borderRadius: '13.4px',
                                        height: '40px',
                                        width: '219px',
                                        position: 'relative',
                                        display: 'flex' // Use flexbox for equal distribution
                                    }
                                },
                                    // Animated pill with JavaScript positioning
                                    React.createElement('div', {
                                        ref: qualityPillRef,
                                        className: 'absolute bg-blue-600 transition-transform duration-300 ease-in-out h-full',
                                        style: {
                                            width: 'calc(33.333% - 0px)', // Exact third with no gaps
                                            borderRadius: '13.4px',
                                            left: '0px', // Start at left edge
                                            top: '0px',
                                            transform: `translateX(${selectedQuality === 'low' ? '0%' : selectedQuality === 'normal' ? '100%' : '200%'})`
                                        }
                                    }),
                                    // Buttons with flexbox for perfect distribution
                                    ['low','normal','hd'].map((qual, idx) => (
                                        React.createElement('button', {
                                            key: qual,
                                            onClick: () => handleQualityChange(qual),
                                            className: `relative z-10 text-sm font-semibold py-1 transition-colors duration-200 flex-1 ${selectedQuality===qual ? 'text-white' : 'text-gray-400 hover:text-gray-300'}`,
                                            style: { outline: 'none' },
                                            'aria-pressed': selectedQuality===qual
                                        }, qual==='normal'?'Medium':qual.toUpperCase())
                                    ))
                                )
                            ),
                            // Action buttons
                            React.createElement('div', { 
                                className: 'action-buttons flex gap-3',
                                id: 'action-buttons-row'
                            },
                                React.createElement(GenerateButton, {
                                    onClick: handleGenerateClick,
                                    isLoading: isLoading,
                                    includePerson: includePerson,
                                    textOverlay: textOverlay
                                }),
                                React.createElement(DownloadButton, {
                                    imageURL: imageURL,
                                    isLoading: isLoading
                                })
                            )
                        )
                    )
                ),

                // === Right Sidebar (Thumbnail Preview) ===
                React.createElement('div', {
                    className: 'right-sidebar p-6',
                    id: 'right-sidebar-preview'
                },
                    // Coming Soon placeholder
                    React.createElement('div', {
                        className: 'coming-soon-container flex flex-col items-center justify-center h-full text-center'
                    },
                        React.createElement('div', {
                            className: 'coming-soon-icon w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mb-4'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                fill: 'none',
                                viewBox: '0 0 24 24',
                                stroke: 'currentColor',
                                className: 'w-8 h-8 text-gray-500'
                            },
                                React.createElement('path', {
                                    strokeLinecap: 'round',
                                    strokeLinejoin: 'round',
                                    strokeWidth: 1.5,
                                    d: 'M12 6v6m0 0v6m0-6h6m-6 0H6'
                                })
                            )
                        ),
                        React.createElement('h3', {
                            className: 'text-lg font-medium text-gray-300 mb-2'
                        }, 'Coming Soon'),
                        React.createElement('p', {
                            className: 'text-sm text-gray-500 max-w-xs'
                        }, 'This space is reserved for future features like generation history, export options, and advanced settings.')
                    )
                )
            )
        )
    );
};

// ======================================================

// Render the App to the DOM
// Ensure the DOM element exists before rendering
console.log("DOMContentLoaded event fired");
const container = document.getElementById('root');
console.log("Root container element:", container);
if (container) {
    console.log("Attempting React render...");
    const root = ReactDOM.createRoot(container);
    root.render(React.createElement(App));
    console.log("React render call executed.");
} else {
    console.error("Root element not found");
} 

