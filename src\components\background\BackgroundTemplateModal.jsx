/**
 * BackgroundTemplateModal.jsx
 * Modal for selecting background styles from a grid of previews
 * Displayed when a non-solid category is clicked
 */

const React = window.React;
const { useState, useEffect, useCallback, useRef } = React;

import { UNSPLASH_PLACEHOLDERS } from '../../utils/backgroundConfig.js';

export const BackgroundTemplateModal = ({
  category,
  onClose,
  onSelectStyle,
  selectedStyleId
}) => {
  const [showAllStyles, setShowAllStyles] = useState(false);
  const [imageErrors, setImageErrors] = useState({});
  const modalRef = useRef(null);
  
  // Number of styles to show initially
  const initialDisplayCount = 8; // Default: 8 items (4x2 grid)
  
  // Calculate styles to display (limited or all)
  const stylesToDisplay = showAllStyles 
    ? category.styles 
    : category.styles.slice(0, Math.min(initialDisplayCount, category.styles.length));
  
  // Handle escape key to close modal
  const handleEscKey = useCallback((e) => {
    if (e.key === 'Escape') onClose();
  }, [onClose]);
  
  // Click outside to close
  const handleClickOutside = useCallback((e) => {
    if (modalRef.current && !modalRef.current.contains(e.target)) {
      onClose();
    }
  }, [modalRef, onClose]);
  
  // Handle image load error
  const handleImageError = (styleId) => {
    setImageErrors(prev => ({
      ...prev,
      [styleId]: true
    }));
  };
  
  // Get appropriate image source - either style preview or placeholder
  const getImageSource = (style) => {
    // If image previously had an error or doesn't have a preview path
    if (imageErrors[style.id] || !style.preview) {
      // First try style-specific placeholder
      if (UNSPLASH_PLACEHOLDERS[style.id]) {
        return UNSPLASH_PLACEHOLDERS[style.id];
      }
      // Then fall back to category placeholder
      return UNSPLASH_PLACEHOLDERS[category.id] || UNSPLASH_PLACEHOLDERS.default;
    }
    return style.preview;
  };
  
  // Add event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleEscKey);
    document.addEventListener('mousedown', handleClickOutside);
    
    // Set body to non-scrollable when modal is open
    document.body.style.overflow = 'hidden';
    
    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.removeEventListener('mousedown', handleClickOutside);
      
      // Restore scrolling when modal is closed
      document.body.style.overflow = 'auto';
    };
  }, [handleEscKey, handleClickOutside]);
  
  // Create the "Show More" card
  const renderShowMoreCard = () => {
    // Only show if there are more styles to display
    if (showAllStyles || category.styles.length <= initialDisplayCount) return null;
    
    return React.createElement('div', {
      id: 'bgModal-showMoreCard',
      className: 'show-more-card flex flex-col items-center justify-center p-4 bg-gray-800/60 border border-gray-600 rounded-lg hover:bg-gray-700/70 hover:border-purple-400 transition-all h-full cursor-pointer bgModal-showMoreCard',
      onClick: () => setShowAllStyles(true),
      tabIndex: '0',
      role: 'button',
      'aria-label': 'Show more background styles',
      onKeyDown: (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          setShowAllStyles(true);
          e.preventDefault();
        }
      }
    },
      React.createElement('span', { 
        className: 'iconify text-3xl mb-2 text-purple-400 bgModal-showMoreIcon', 
        'data-icon': 'solar:add-circle-bold-duotone'
      }),
      React.createElement('span', { className: 'text-sm text-gray-300 bgModal-showMoreText' }, 'Show More Styles')
    );
  };
  
  // Render a style preview card
  const renderStyleCard = (style) => {
    const isSelected = selectedStyleId === style.id;
    const imageSrc = getImageSource(style);
    const isPlaceholder = imageErrors[style.id] || !style.preview;
    
    // Regular style preview card
    return React.createElement('div', {
      key: style.id,
      id: `bgModal-styleCard-${style.id}`,
      role: 'gridcell', 
      tabIndex: '0',
      className: `style-preview-card relative overflow-hidden border-2 rounded-lg cursor-pointer ${isSelected ? 'border-purple-500 shadow-lg shadow-purple-900/30' : 'border-gray-700 hover:border-purple-400 hover:shadow-md'} h-full transition-all focus:outline-none focus:ring-2 focus:ring-purple-500 bgModal-styleCard`,
      'aria-label': `Select ${style.name} style`,
      'aria-selected': isSelected,
      onClick: () => onSelectStyle(style.id),
      onKeyDown: (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onSelectStyle(style.id);
          e.preventDefault();
        }
      }
    },
      // Top indicator for selected style
      isSelected && React.createElement('div', {
        className: 'absolute top-2 right-2 z-10 bg-purple-600 rounded-full p-1 shadow-lg bgModal-selectedIndicator'
      },
        React.createElement('span', { 
          className: 'iconify text-white text-sm bgModal-checkIcon', 
          'data-icon': 'solar:check-circle-bold'
        })
      ),
      
      // Style preview image
      React.createElement('div', { 
        className: 'aspect-video w-full overflow-hidden bg-gray-800 bgModal-previewContainer relative' 
      },
        // Image (either actual preview or Unsplash placeholder)
        React.createElement('img', {
          src: imageSrc,
          alt: style.name,
          className: 'w-full h-full object-cover hover:scale-105 transition-transform bgModal-previewImage',
          onError: () => handleImageError(style.id)
        }),
        // Placeholder overlay badge (only shown when using a placeholder)
        /* Removed placeholder indicator as requested */
      ),
      
      // Style name and description
      React.createElement('div', {
        className: 'p-3 bg-gray-800 bgModal-styleInfo'
      },
        React.createElement('div', { className: 'flex justify-between items-start' },
          React.createElement('h4', {
            className: 'text-sm font-medium text-gray-200 bgModal-styleName flex items-center gap-1'
          }, 
            style.icon && React.createElement('span', { 
              className: 'iconify text-purple-400 text-xs bgModal-styleIcon', 
              'data-icon': style.icon 
            }),
            style.name
          )
        ),
        React.createElement('p', {
          className: 'text-xs text-gray-400 mt-1 line-clamp-2 bgModal-styleDescription'
        }, style.description || `${category.name} style for your thumbnail background.`)
      )
    );
  };

  // Render the modal with a blurred backdrop and centered content
  return React.createElement('div', {
    id: 'bgModal-backdrop',
    className: 'modal-overlay bg-black/60 backdrop-blur-sm bgModal-backdrop',
    role: 'dialog',
    'aria-labelledby': 'background-modal-title',
    'aria-modal': 'true'
  },
    // Modal content
    React.createElement('div', {
      id: 'bgModal-container',
      ref: modalRef,
      className: 'modal-popup bg-gray-900 rounded-xl shadow-2xl max-w-5xl w-[95%] max-h-[90vh] flex flex-col overflow-hidden transform transition-all animate-modalFadeIn bgModal-container',
    },
      // Modal header
      React.createElement('div', { 
        id: 'bgModal-header',
        className: 'px-6 py-4 border-b border-gray-800 flex items-center justify-between sticky top-0 bg-gray-900 z-10 bgModal-header'
      },
        React.createElement('h3', {
          id: 'background-modal-title',
          className: 'text-xl font-semibold text-gray-200 flex items-center gap-2 bgModal-title'
        },
          React.createElement('span', { 
            className: 'iconify text-purple-400 bgModal-categoryIcon', 
            'data-icon': category.icon 
          }),
          `${category.name} Background Styles`
        ),
        React.createElement('button', {
          id: 'bgModal-closeBtn',
          onClick: onClose,
          className: 'text-gray-400 hover:text-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 rounded-full p-1 bgModal-closeButton',
          'aria-label': 'Close modal'
        },
          React.createElement('span', { 
            className: 'iconify text-2xl bgModal-closeIcon', 
            'data-icon': 'solar:close-circle-bold-duotone'
          })
        )
      ),
      
      // Modal body - Style grid
      React.createElement('div', {
        id: 'bgModal-body',
        className: 'p-6 overflow-y-auto flex-1 bg-gray-900/95 bgModal-body'
      },
        // Description
        React.createElement('p', { 
          id: 'bgModal-description',
          className: 'text-sm text-gray-400 mb-5 bgModal-description' 
        }, category.description || `Choose a ${category.name.toLowerCase()} style for your thumbnail background.`),
        
        // Grid of styles
        React.createElement('div', {
          id: 'bgModal-styleGrid',
          role: 'grid',
          'aria-label': `${category.name} style options`,
          className: 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 auto-rows-fr bgModal-styleGrid'
        },
          // Map all visible styles
          stylesToDisplay.map(style => renderStyleCard(style)),
          
          // Show More card (if needed)
          !showAllStyles && category.styles.length > initialDisplayCount && renderShowMoreCard()
        )
      ),
      
      // Modal footer
      React.createElement('div', {
        id: 'bgModal-footer',
        className: 'px-6 py-4 border-t border-gray-800 flex justify-end sticky bottom-0 bg-gray-900 z-10 bgModal-footer'
      },
        React.createElement('button', {
          id: 'bgModal-closeFooterBtn',
          onClick: onClose,
          className: 'px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 bgModal-footerCloseBtn',
          'aria-label': 'Close dialog'
        }, 'Close')
      )
    )
  );
};

export default BackgroundTemplateModal; 