export default [
  {
    id: "reaction-epic",
    name: "Epic [TOPIC] Reaction!",
    description: "For epic reaction videos.",
    promptBase: "Create a fun YouTube thumbnail for 'Epic [TOPIC] Reaction!'. Show a person with a shocked expression, emoji and explosion icons, and bold text overlay: 'EPIC REACTION!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Shocked', includeIcons: true, textOverlay: true, overlayText: "EPIC\nREACTION!" },
    templateImagePlaceholder: { text: "Epic Reaction", bgColor: "bg-pink-500" }
  },
  {
    id: "reaction-meme",
    name: "[MEME] Meme Reaction!",
    description: "For meme reaction videos.",
    promptBase: "Design a meme reaction thumbnail for '[MEME] Meme Reaction!'. Show a person laughing, meme and laughing emoji icons, and text overlay: 'MEME REACTION!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Laughing', includeIcons: true, textOverlay: true, overlayText: "MEME\nREACTION!" },
    templateImagePlaceholder: { text: "Meme Reaction", bgColor: "bg-pink-400" }
  },
  {
    id: "reaction-music",
    name: "[SONG] Music Reaction!",
    description: "For music reaction videos.",
    promptBase: "Create a music reaction thumbnail for '[SONG] Music Reaction!'. Show a person with headphones, music note and heart icons, and bold text overlay: 'MUSIC REACTION!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Excited', includeIcons: true, textOverlay: true, overlayText: "MUSIC\nREACTION!" },
    templateImagePlaceholder: { text: "Music Reaction", bgColor: "bg-pink-300" }
  },
  {
    id: "reaction-try-not-to-laugh",
    name: "Try Not To Laugh Challenge!",
    description: "For try not to laugh challenge videos.",
    promptBase: "Design a challenge thumbnail for 'Try Not To Laugh Challenge!'. Show two people, one laughing and one serious, with challenge and timer icons. Text overlay: 'TRY NOT TO LAUGH!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Laughing', includeIcons: true, textOverlay: true, overlayText: "TRY NOT TO LAUGH!" },
    templateImagePlaceholder: { text: "Try Not To Laugh", bgColor: "bg-pink-200" }
  },
  {
    id: "reaction-food",
    name: "[FOOD] Taste Test Reaction!",
    description: "For food taste test reaction videos.",
    promptBase: "Create a taste test reaction thumbnail for '[FOOD] Taste Test Reaction!'. Show a person with a surprised expression, food and star icons, and bold text overlay: 'TASTE TEST!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Surprised', includeIcons: true, textOverlay: true, overlayText: "TASTE TEST!" },
    templateImagePlaceholder: { text: "Taste Test", bgColor: "bg-pink-100" }
  },
  {
    id: "reaction-review",
    name: "[PRODUCT] First Impression!",
    description: "For first impression or review reactions.",
    promptBase: "Design a first impression thumbnail for '[PRODUCT] First Impression!'. Show a person with a curious expression, product and thumbs up/down icons, and text overlay: 'FIRST IMPRESSION!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Curious', includeIcons: true, textOverlay: true, overlayText: "FIRST IMPRESSION!" },
    templateImagePlaceholder: { text: "First Impression", bgColor: "bg-pink-50" }
  }
]; 