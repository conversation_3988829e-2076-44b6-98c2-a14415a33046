export default [
  {
    id: "gaming-highlight",
    name: "[GAME] Epic Highlight!",
    description: "For gaming highlight and gameplay videos.",
    promptBase: "Create a dynamic YouTube thumbnail for '[GAME] Epic Highlight!'. Show an action-packed game scene with neon effects and a person celebrating. Text overlay: 'EPIC HIGHLIGHT!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Excited', textOverlay: true, overlayText: "EPIC\nHIGHLIGHT!" },
    templateImagePlaceholder: { text: "Highlight", bgColor: "bg-green-500" }
  },
  {
    id: "gaming-vs",
    name: "[PLAYER 1] vs [PLAYER 2] Showdown",
    description: "Versus-style 1v1 or team battle.",
    promptBase: "Design a split-screen thumbnail for '[PLAYER 1] vs [PLAYER 2]'. Use game icons, dramatic lighting, and bold text overlay: '[PLAYER 1] VS [PLAYER 2]'.",
    settingsToApply: { includePerson: false, includeIcons: true, textOverlay: true, overlayText: "[PLAYER 1] VS [PLAYER 2]" },
    templateImagePlaceholder: { text: "Showdown", bgColor: "bg-green-400" }
  },
  {
    id: "gaming-loadout",
    name: "Best [GAME] Loadout!",
    description: "For best weapons/gear guides.",
    promptBase: "Create a bold thumbnail for 'Best [GAME] Loadout!'. Show a person holding top gear, with weapon and shield icons. Text overlay: 'BEST LOADOUT!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Confident', includeIcons: true, textOverlay: true, overlayText: "BEST\nLOADOUT!" },
    templateImagePlaceholder: { text: "Loadout", bgColor: "bg-green-300" }
  },
  {
    id: "gaming-reaction",
    name: "[GAME] Funny Reaction!",
    description: "For funny or surprising gaming moments.",
    promptBase: "Design a fun thumbnail for '[GAME] Funny Reaction!'. Show a person with a shocked expression, emoji and controller icons, and bold text overlay: 'FUNNY REACTION!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Shocked', includeIcons: true, textOverlay: true, overlayText: "FUNNY\nREACTION!" },
    templateImagePlaceholder: { text: "Reaction", bgColor: "bg-green-200" }
  },
  {
    id: "gaming-guide",
    name: "[GAME] Pro Guide!",
    description: "For pro tips and strategy guides.",
    promptBase: "Create a pro guide thumbnail for '[GAME] Pro Guide!'. Use a person pointing, map and trophy icons, and text overlay: 'PRO GUIDE!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Pointing', includeIcons: true, textOverlay: true, overlayText: "PRO\nGUIDE!" },
    templateImagePlaceholder: { text: "Pro Guide", bgColor: "bg-green-100" }
  },
  {
    id: "gaming-update",
    name: "[GAME] Update News!",
    description: "For patch/update news and reactions.",
    promptBase: "Design a news-style thumbnail for '[GAME] Update News!'. Show a person with a surprised expression, update and lightning icons, and bold text overlay: 'UPDATE NEWS!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Surprised', includeIcons: true, textOverlay: true, overlayText: "UPDATE\nNEWS!" },
    templateImagePlaceholder: { text: "Update News", bgColor: "bg-green-50" }
  }
]; 