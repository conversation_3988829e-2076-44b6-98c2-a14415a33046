/**
 * CollapsibleSection.jsx
 * Reusable component for creating collapsible sections in the sidebar
 */

const React = window.React;
const { useState, useEffect } = React;

export const CollapsibleSection = ({
  title,
  icon,
  defaultExpanded = false,
  children,
  id
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  // Check localStorage for saved state on mount
  useEffect(() => {
    const savedState = localStorage.getItem(`section_expanded_${id}`);
    if (savedState !== null) {
      setIsExpanded(savedState === 'true');
    }
  }, [id]);

  // Save state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem(`section_expanded_${id}`, isExpanded);
  }, [isExpanded, id]);

  const toggleExpanded = () => {
    setIsExpanded(prev => !prev);
  };

  return React.createElement('div', {
    className: 'collapsible-section border-t border-gray-700 mt-2 pt-4',
    id: `section-${id}`
  },
    // Header with toggle
    React.createElement('div', {
      className: 'flex items-center justify-between cursor-pointer',
      onClick: toggleExpanded,
      role: 'button',
      'aria-expanded': isExpanded,
      tabIndex: 0,
      onKeyDown: (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          toggleExpanded();
          e.preventDefault();
        }
      }
    },
      // Title with icon
      React.createElement('h3', {
        className: 'text-md font-medium text-purple-300 flex items-center gap-1.5'
      },
        icon && React.createElement('span', {
          className: 'iconify',
          'data-icon': icon
        }),
        title
      ),
      // Chevron icon
      React.createElement('span', {
        className: 'iconify',
        'data-icon': 'solar:alt-arrow-down-linear'
      })
    ),
    
    // Collapsible content
    React.createElement('div', {
      // When the section is expanded we allow overflow so internal tooltips (which use absolute positioning) are not clipped.
      className: `collapsible-content transition-all duration-300 ease-in-out ${isExpanded ? 'max-h-screen opacity-100 mt-4 overflow-visible' : 'max-h-0 opacity-0 mt-0 overflow-hidden'}`,
      'aria-hidden': !isExpanded
    },
      children
    )
  );
};

export default CollapsibleSection; 