/**
 * Enhanced Typewriter animation utility for prompt input suggestions
 * Displays rotating placeholder suggestions with a smooth, realistic typing animation
 */

// Array of suggested example prompts to display as typing placeholders
export const suggestedPrompts = [
  // Gaming Thumbnails
  "Create a gaming thumbnail: Epic 1v5 clutch in Valorant with intense action shot",
  "Design a gaming thumbnail: World Record Speedrun in Minecraft with timer overlay",
  "Make a gaming thumbnail: Ultimate Team Pack Opening in FIFA with shocked reaction",
  "Create a gaming thumbnail: Epic 1v5 clutch in Valorant with intense action shot",
  "call of duty thumbnail: epic 1v5 clutch in Call of Duty with intense action shot",
  
  // Tech Thumbnails
  "Generate a tech review thumbnail: 'M3 MacBook Pro Review: Game Changer?' with sleek setup",
  "Create a tech thumbnail: Building a $5000 Gaming PC with RGB showcase",
  "Design a tech thumbnail: 'iPhone 15 Pro Max vs Pixel 8 Pro' camera comparison",
  
  // Random Thumbnails
  "Make a cooking thumbnail: <PERSON>'s Secret Recipe Revealed with dramatic plating",
  "Generate a travel thumbnail: Hidden Gems in Bali with stunning sunset backdrop",
  "Create a DIY thumbnail: Transforming My Room for Under $100 with before/after",
  "Design a music thumbnail: Behind the Scenes of My First Music Video with studio setup",
  
  // Fitness & Nutrition Thumbnails
  "Create a fitness thumbnail: 30-Day Calisthenics Transformation with progress shots",
  "Design a nutrition thumbnail: Meal Prep for Muscle Gain with macro breakdown",
  "Make a workout thumbnail: Home HIIT Routine That Actually Works with timer display"

];

// Enhanced configuration options for smoother animation
const typewriterConfig = {
  baseTypingSpeed: 45,      // Base milliseconds per character
  speedVariation: 25,       // Random variation in typing speed (±25ms)
  fastTypingChance: 0.3,    // 30% chance for faster typing bursts
  pauseOnPunctuation: 150,  // Extra pause after punctuation
  holdDuration: 5000,       // How long to show completed text (increased to 3s)
  erasingSpeed: 25,         // Faster erasing speed
  pauseBetween: 800,        // Pause between sentences (increased for smoother transition)
  cursorChar: "█",          // Block cursor for terminal feel
  cursorBlinkSpeed: 530,    // Slightly irregular blink
  fadeTransitionMs: 300,     // Fade transition duration
  enableSoundEffects: false // Optional keystroke sounds (disabled by default)
};

/**
 * Optional subtle keystroke sound effect
 * Creates a very quiet, brief audio feedback for each character
 */
const playKeystrokeSound = (() => {
  let audioContext = null;
  let isAudioEnabled = false;
  
  // Initialize audio context only when needed
  const initAudio = () => {
    if (!audioContext && window.AudioContext) {
      try {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        isAudioEnabled = true;
      } catch (e) {
        console.log('Audio context not available');
        isAudioEnabled = false;
      }
    }
  };
  
  return (char) => {
    if (!typewriterConfig.enableSoundEffects || !isAudioEnabled) return;
    
    if (!audioContext) initAudio();
    if (!audioContext) return;
    
    try {
      // Create a very subtle, brief tone
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      // Different frequencies for different character types
      let frequency = 800; // Default
      if (/[aeiou]/i.test(char)) frequency = 750; // Vowels slightly lower
      if (/[.!?]/.test(char)) frequency = 900; // Punctuation higher
      if (/\s/.test(char)) frequency = 600; // Spaces lower
      
      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
      oscillator.type = 'sine';
      
      // Very quiet and brief
      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(0.005, audioContext.currentTime + 0.01); // Very quiet
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.05);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.05);
    } catch (e) {
      // Silently fail if audio doesn't work
    }
  };
})();

/**
 * Enhanced typewriter animation with variable speeds and smooth transitions
 * @param {HTMLElement} inputElement - The textarea/input element to animate
 * @param {Array} prompts - Array of strings to cycle through
 * @param {Function} setPlaceholder - Function to update element's placeholder
 * @param {boolean} enableSounds - Whether to enable subtle keystroke sounds
 * @returns {Object} Animation controller with stop() method
 */
export const startTypewriter = (inputElement, prompts = suggestedPrompts, setPlaceholder, enableSounds = false) => {
  // If no prompts or element, exit early
  if (!prompts.length || !inputElement) {
    return { stop: () => {} };
  }

  // Update sound effects setting
  if (enableSounds !== undefined) {
    typewriterConfig.enableSoundEffects = enableSounds;
  }

  // Animation state
  let currentPromptIndex = Math.floor(Math.random() * prompts.length);
  let isTyping = true;
  let isErasing = false;
  let charIndex = 0;
  let cursorVisible = true;
  let cursorInterval = null;
  let animationTimeout = null;
  let timeoutId = null;
  let isAnimationActive = true;
  
  // Function to check if animation should continue
  const shouldContinue = () => {
    return inputElement.value === '' && isAnimationActive;
  };

  // Calculate variable typing speed with randomization
  const getTypingSpeed = (char, isTypingForward = true) => {
    if (!isTypingForward) return typewriterConfig.erasingSpeed;
    
    let speed = typewriterConfig.baseTypingSpeed;
    
    // Add random variation
    speed += (Math.random() - 0.5) * typewriterConfig.speedVariation;
    
    // Chance for faster typing bursts (simulating skilled typing)
    if (Math.random() < typewriterConfig.fastTypingChance) {
      speed *= 0.6; // 40% faster
    }
    
    // Pause longer after punctuation
    if (/[.!?,:;]/.test(char)) {
      speed += typewriterConfig.pauseOnPunctuation;
    }
    
    // Slightly slower for capital letters (simulating shift key)
    if (/[A-Z]/.test(char)) {
      speed += 15;
    }
    
    return Math.max(20, speed); // Minimum 20ms
  };

  // Enhanced cursor blinking with slight irregularity
  const startCursorBlink = () => {
    const blink = () => {
      if (!shouldContinue()) return;
      
      cursorVisible = !cursorVisible;
      updatePlaceholder();
      
      // Slightly irregular blink timing for more natural feel
      const nextBlink = typewriterConfig.cursorBlinkSpeed + (Math.random() - 0.5) * 100;
      cursorInterval = setTimeout(blink, nextBlink);
    };
    blink();
  };

  // Add subtle visual feedback for each keystroke
  const addKeystrokeFeedback = () => {
    if (inputElement && inputElement.style) {
      // Subtle text shadow pulse
      inputElement.style.textShadow = '0 0 8px rgba(139, 92, 246, 0.3)';
      setTimeout(() => {
        if (inputElement.style) {
          inputElement.style.textShadow = '';
        }
      }, 100);
    }
  };

  // Update the placeholder text with current animation state
  const updatePlaceholder = () => {
    if (!shouldContinue()) {
      stopAnimation();
      return;
    }
    
    const currentPrompt = prompts[currentPromptIndex];
    const displayText = currentPrompt.substring(0, charIndex);
    const cursorDisplay = cursorVisible ? typewriterConfig.cursorChar : ' ';
    
    if (typeof setPlaceholder === 'function') {
      setPlaceholder(displayText + cursorDisplay);
    } else {
      inputElement.placeholder = displayText + cursorDisplay;
    }
  };

  // Enhanced animation step with variable timing
  const animationStep = () => {
    if (!shouldContinue()) {
      stopAnimation();
      return;
    }

    const currentPrompt = prompts[currentPromptIndex];
    
    if (isTyping) {
      // Typing forward
      if (charIndex < currentPrompt.length) {
        const currentChar = currentPrompt[charIndex];
        charIndex++;
        updatePlaceholder();
        addKeystrokeFeedback();
        playKeystrokeSound(currentChar);
        
        // Schedule next character with variable timing
        const nextSpeed = getTypingSpeed(currentChar, true);
        animationTimeout = setTimeout(animationStep, nextSpeed);
      } else {
        // Reached end of current prompt, hold before erasing
        isTyping = false;
        timeoutId = setTimeout(() => {
          if (shouldContinue()) {
            isErasing = true;
            animationStep();
          }
        }, typewriterConfig.holdDuration);
      }
    } else if (isErasing) {
      // Erasing with consistent speed
      if (charIndex > 0) {
        charIndex--;
        updatePlaceholder();
        playKeystrokeSound(currentPrompt[charIndex]);
        
        const nextSpeed = getTypingSpeed('', false);
        animationTimeout = setTimeout(animationStep, nextSpeed);
      } else {
        // Finished erasing, move to next prompt
        isErasing = false;
        isTyping = true;
        currentPromptIndex = (currentPromptIndex + 1) % prompts.length;
        
        timeoutId = setTimeout(() => {
          if (shouldContinue()) {
            animationStep();
          }
        }, typewriterConfig.pauseBetween);
      }
    }
  };

  // Start the animation with fade-in effect
  const startAnimation = () => {
    if (inputElement && inputElement.style) {
      // Add fade-in transition
      inputElement.style.transition = `opacity ${typewriterConfig.fadeTransitionMs}ms ease-in-out`;
      inputElement.style.opacity = '0.7';
      
      setTimeout(() => {
        if (inputElement.style) {
          inputElement.style.opacity = '1';
        }
      }, 100);
    }
    
    startCursorBlink();
    animationStep();
  };

  // Stop all animations and clear timers with fade-out
  const stopAnimation = () => {
    isAnimationActive = false;
    
    clearTimeout(cursorInterval);
    clearTimeout(animationTimeout);
    clearTimeout(timeoutId);
    
    // Fade out effect
    if (inputElement && inputElement.style) {
      inputElement.style.transition = `opacity ${typewriterConfig.fadeTransitionMs}ms ease-in-out`;
      inputElement.style.opacity = '0.8';
      
      setTimeout(() => {
        if (inputElement.style) {
          inputElement.style.opacity = '1';
          inputElement.style.transition = '';
          inputElement.style.textShadow = '';
        }
      }, typewriterConfig.fadeTransitionMs);
    }
    
    // Reset placeholder to default
    if (typeof setPlaceholder === 'function') {
      setPlaceholder('');
    } else {
      inputElement.placeholder = '';
    }
  };

  // Start the animation immediately
  startAnimation();
  
  // Return controller with enhanced methods
  return {
    stop: stopAnimation,
    restart: () => {
      stopAnimation();
      setTimeout(() => {
        isAnimationActive = true;
        startAnimation();
      }, 100);
    },
    pause: () => {
      isAnimationActive = false;
    },
    resume: () => {
      isAnimationActive = true;
      animationStep();
    }
  };
};

/**
 * Checks if user prefers reduced motion
 * @returns {boolean} Whether reduced motion is preferred
 */
export const prefersReducedMotion = () => {
  return window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

/**
 * Smooth typewriter animation for improve button output text
 * Creates a realistic typing effect for displaying improved prompts
 * @param {string} text - The text to animate
 * @param {Function} setVisibleText - Function to update the visible text
 * @param {Function} setUserPrompt - Function to update the user prompt when complete
 * @param {Function} setIsTyping - Function to control typing state
 * @param {Function} setCursorVisible - Function to control cursor visibility
 * @returns {Object} Animation controller with stop() method
 */
export const startImproveTypewriter = (text, setVisibleText, setUserPrompt, setIsTyping, setCursorVisible) => {
  if (!text || typeof setVisibleText !== 'function') {
    return { stop: () => {} };
  }

  // Animation state
  let charIndex = 0;
  let cursorVisible = true;
  let cursorInterval = null;
  let animationTimeout = null;
  let isAnimationActive = true;

  // Configuration for improve text animation
  const improveConfig = {
    baseTypingSpeed: 35,      // Slightly faster than suggestions
    speedVariation: 20,       // Less variation for smoother feel
    fastTypingChance: 0.4,    // More frequent fast bursts
    pauseOnPunctuation: 120,  // Shorter pauses
    cursorChar: "█",          // Block cursor
    cursorBlinkSpeed: 500,    // Regular blink
    enableSoundEffects: false // Disabled by default
  };

  // Calculate variable typing speed
  const getTypingSpeed = (char) => {
    let speed = improveConfig.baseTypingSpeed;
    
    // Add random variation
    speed += (Math.random() - 0.5) * improveConfig.speedVariation;
    
    // Chance for faster typing bursts
    if (Math.random() < improveConfig.fastTypingChance) {
      speed *= 0.7; // 30% faster
    }
    
    // Pause longer after punctuation
    if (/[.!?,:;]/.test(char)) {
      speed += improveConfig.pauseOnPunctuation;
    }
    
    // Slightly slower for capital letters
    if (/[A-Z]/.test(char)) {
      speed += 10;
    }
    
    return Math.max(15, speed); // Minimum 15ms
  };

  // Cursor blinking
  const startCursorBlink = () => {
    const blink = () => {
      if (!isAnimationActive) return;
      
      cursorVisible = !cursorVisible;
      updateVisibleText();
      
      cursorInterval = setTimeout(blink, improveConfig.cursorBlinkSpeed);
    };
    blink();
  };

  // Update the visible text with current animation state
  const updateVisibleText = () => {
    if (!isAnimationActive) return;
    
    const displayText = text.substring(0, charIndex);
    const cursorDisplay = cursorVisible ? improveConfig.cursorChar : ' ';
    
    setVisibleText(displayText + cursorDisplay);
  };

  // Main animation step
  const animationStep = () => {
    if (!isAnimationActive) return;

    if (charIndex < text.length) {
      const currentChar = text[charIndex];
      charIndex++;
      updateVisibleText();
      
      // Optional keystroke sound
      if (improveConfig.enableSoundEffects) {
        playKeystrokeSound(currentChar);
      }
      
      // Schedule next character with variable timing
      const nextSpeed = getTypingSpeed(currentChar);
      animationTimeout = setTimeout(animationStep, nextSpeed);
    } else {
      // Animation complete
      stopAnimation();
      // Set final text without cursor
      setVisibleText(text);
      setUserPrompt(text);
      setIsTyping(false);
      setCursorVisible(false);
    }
  };

  // Start the animation
  const startAnimation = () => {
    setIsTyping(true);
    setCursorVisible(true);
    setVisibleText('');
    startCursorBlink();
    animationStep();
  };

  // Stop all animations and clear timers
  const stopAnimation = () => {
    isAnimationActive = false;
    
    clearTimeout(cursorInterval);
    clearTimeout(animationTimeout);
    
    setCursorVisible(false);
  };

  // Start the animation immediately
  startAnimation();
  
  // Return controller
  return {
    stop: stopAnimation
  };
}; 