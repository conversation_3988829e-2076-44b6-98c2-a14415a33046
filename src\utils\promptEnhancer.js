export const enhancePrompt = (raw) => {
  if (!raw) return '';

  // 1. Strip emojis and non-ASCII pictographs
  let txt = raw.replace(/[\p{Extended_Pictographic}]/gu, '').trim();

  // 2. Lowercase for easier replacement
  txt = txt.toLowerCase();

  // 3. Remove personal pronouns / filler verbs
  const stopWords = [
    'i', 'my', 'me', 'mine', 'we', 'our', 'ours', 'you', 'your',
    'use', 'using', 'show', 'shows', 'showing', 'explain', 'explaining',
    'react', 'reacting', 'study', 'studying', 'learn', 'learning',
    'talk', 'talking', 'give', 'giving', 'take', 'taking', 'make', 'making',
    'about', 'the', 'a', 'an', 'of', 'to', 'for', 'on', 'in' // common fillers
  ];
  stopWords.forEach(w => {
    const re = new RegExp(`\\b${w}\\b`, 'gi');
    txt = txt.replace(re, ' ');
  });

  // Collapse multiple spaces and trim
  txt = txt.replace(/\s{2,}/g, ' ').trim();

  if (!txt) return '';

  // 4. Keyword→scene mapping
  const mappings = [
    {
      keywords: ['website', 'websites', 'site'],
      scene: 'A modern workspace with a laptop displaying popular study websites, books and a cup of coffee on the desk. Soft daylight, clean background, cinematic composition.'
    },
    {
      keywords: ['tool', 'tools', 'software', 'app', 'apps'],
      scene: 'A sleek tech setup showcasing various AI tools on floating holographic screens, neon accents, dark gradient background.'
    },
    {
      keywords: ['iphone', 'smartphone', 'phone'],
      scene: 'A glowing smartphone centered on a minimalist surface with subtle reflections and dramatic rim lighting.'
    },
    {
      keywords: ['purchase', 'purchases', 'buy', 'buys'],
      scene: 'A dramatic tabletop scene with assorted gadgets under a single spotlight against a dark backdrop.'
    },
    {
      keywords: ['tiktok', 'tiktoks', 'video', 'videos'],
      scene: 'A retro theatre marquee background with film reels and colorful light leaks in a nostalgic style.'
    }
  ];

  for (const map of mappings) {
    if (map.keywords.some(k => txt.includes(k))) {
      return map.scene;
    }
  }

  // 5. Fallback – convert remaining keywords into generic cinematic scene
  return `A cinematic scene illustrating ${txt}, clean background, dramatic lighting.`;
}; 