// Named export for GenerateButton component
export const GenerateButton = ({ onClick, isLoading }) => {

    const handleKeyDown = (event) => {
        // Trigger click on Enter or Space key press for accessibility
        if (!isLoading && (event.key === 'Enter' || event.key === ' ')) {
            onClick();
            event.preventDefault();
        }
    };

    return (
        React.createElement('button', {
            onClick: onClick,
            onKeyDown: handleKeyDown, // Add keyboard accessibility
            disabled: isLoading,
            className: 'w-full mt-4 px-6 py-3 bg-purple-600 text-white font-semibold rounded-md shadow-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition duration-150 ease-in-out', // Added focus, disabled, flex centering, transition
            'aria-label': isLoading ? 'Generating thumbnail, please wait' : 'Generate thumbnail based on current settings'
        },
            isLoading ?
                // Loading indicator
                React.createElement(React.Fragment, null,
                    React.createElement('svg', {
                         className: "animate-spin -ml-1 mr-3 h-5 w-5 text-white",
                         xmlns: "http://www.w3.org/2000/svg",
                         fill: "none",
                         viewBox: "0 0 24 24"
                     },
                         React.createElement('circle', { 
                             className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4" 
                         }),
                         React.createElement('path', { 
                             className: "opacity-75", 
                             fill: "currentColor", 
                             d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" 
                         })
                    ),
                    'Generating...'
                )
                :
                // Default text
                'Generate Thumbnail'
        )
    );
};

// Export the component for dynamic import
export default GenerateButton; 