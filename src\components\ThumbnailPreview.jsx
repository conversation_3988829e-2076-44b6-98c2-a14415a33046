// Named export for ThumbnailPreview component
export const ThumbnailPreview = ({ imageURL, isLoading, overlayText, selectedFontFamily, selectedTextSize, primaryTextColor, secondaryTextColor, isModalOpen }) => {

    // Placeholder content when no image is loaded or during loading
    const placeholder = React.createElement('div', {
        className: 'w-full h-full flex flex-col items-center justify-center bg-gray-700 border-2 border-dashed border-gray-500 rounded-lg text-gray-400'
    },
        isLoading ? React.createElement('div', null, 
                // Simple spinner using Tailwind
                React.createElement('div', { 
                    className: 'animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-purple-500' 
                }),
                React.createElement('p', { className: 'mt-4 text-lg' }, 'Generating Thumbnail...')
            ) :
            React.createElement(React.Fragment, null,
                // Solar Icon placeholder (Iconify syntax)
                React.createElement('span', {
                    className: 'iconify w-16 h-16 text-gray-500 mb-4',
                    'data-icon': 'solar:gallery-round-bold-duotone',
                    style: { display: 'block' }
                }),
                React.createElement('p', null, 'Your Thumbnail Preview here')
            )
    );

    // Image element if URL exists
    const imageElement = imageURL ? React.createElement('img', {
        src: imageURL,
        alt: 'Generated YouTube Thumbnail',
        className: 'object-cover w-full h-full' // Use CSS classes instead of inline styles
    }) : null;

    return (
        React.createElement('div', { 
            className: 'panel-preview-container relative overflow-hidden preview-container',
            style: isModalOpen ? { zIndex: -1 } : undefined
        }, 
            imageURL ? imageElement : placeholder,
            overlayText && React.createElement('div', {
                className: 'absolute inset-0 flex flex-col items-center justify-center pointer-events-none',
                style: { zIndex: 10 }
            },
                (overlayText || '').toUpperCase().split('\n').map((line, idx) =>
                    React.createElement('span', {
                        key: idx,
                        style: {
                            fontFamily: selectedFontFamily || 'Impact, Arial, sans-serif',
                            fontSize: selectedTextSize === 'Large' ? '3.5rem' : '2.5rem',
                            fontWeight: 'bold',
                            textTransform: 'uppercase',
                            letterSpacing: '0.02em',
                            lineHeight: '1.1',
                            marginBottom: '0.2em',
                            background: `linear-gradient(90deg, ${primaryTextColor}, ${secondaryTextColor})`,
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            backgroundClip: 'text',
                            color: 'transparent',
                            textShadow: '2px 2px 8px rgba(0,0,0,0.5)',
                            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.4))',
                            maxWidth: '90%',
                            whiteSpace: 'pre-line',
                            textAlign: 'center',
                            display: 'block',
                        },
                        className: 'gradient-text',
                    }, line)
                )
            )
        )
    );
};

// Export the component for dynamic import
export default ThumbnailPreview; 