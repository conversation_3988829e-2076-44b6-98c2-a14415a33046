/* Basic body/layout styles */
body {
    @apply bg-gray-900 text-gray-100 font-sans;
    margin: 0; 
    padding: 0;
    overflow-x: hidden; /* Prevent horizontal scroll */
}

.container {
    @apply mx-auto;
}

/* Root layout container */
#root > div {
    @apply min-h-screen;
}

/* ================= THREE-PANEL PROFESSIONAL LAYOUT ================= */

.app-container {
    display: grid;
    grid-template-columns: 368px 1fr 340px; /* Left sidebar increased by 15% (320px → 368px), right sidebar reduced by 15% (400px → 340px) */
    grid-template-rows: 1fr; /* Single row layout */
    min-height: 100vh;
    max-width: 100vw;
    background-color: #111827; /* bg-gray-900 */
    height: 100vh; /* Ensure the app container fills the viewport */
    overflow: visible; /* Allow tooltips and modals to overflow grid boundaries */
}

/* Left Sidebar - Controls */
.left-sidebar {
    grid-column: 1;
    grid-row: 1;
    background-color: #1F2937; /* bg-gray-800 */
    border-right: 1px solid #374151; /* border-gray-700 */
    overflow-y: auto; /* Allow vertical scrolling */
    overflow-x: visible; /* Allow tooltips to overflow horizontally */
    position: sticky;
    top: 0;
    height: 100vh;
    width: 368px;
    padding: 1rem;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

/* Custom dark scrollbar for left sidebar */
.left-sidebar::-webkit-scrollbar {
    width: 10px;
    background: #18181B; /* Very dark background */
}
.left-sidebar::-webkit-scrollbar-thumb {
    background: #27272A; /* Slightly lighter thumb */
    border-radius: 8px;
    border: 2px solid #18181B;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}
.left-sidebar::-webkit-scrollbar-thumb:hover {
    background: #3F3F46;
}
.left-sidebar::-webkit-scrollbar-corner {
    background: #18181B;
}
/* Firefox */
.left-sidebar {
    scrollbar-width: thin;
    scrollbar-color: #27272A #18181B;
}

/* Hide scrollbar for inner design controls scroller */
.left-sidebar .overflow-y-auto {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}
.left-sidebar .overflow-y-auto::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* Main Center Panel - Preview Area */
.main-center-panel {
    grid-column: 2;
    grid-row: 1;
    display: flex;
    flex-direction: column;
    padding: 1.5rem;
    background-color: #111827; /* bg-gray-900 */
    height: 100vh;
    min-height: 0; /* Allow shrinking to fit content */
    overflow-y: auto;
    gap: 1.5rem;
}

/* Right Sidebar - Reserved for future features */
.right-sidebar {
    grid-column: 3;
    grid-row: 1;
    background-color: #1F2937; /* bg-gray-800 */
    border-left: 1px solid #374151; /* border-gray-700 */
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    color: #6B7280; /* text-gray-500 */
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    height: 100vh;
    overflow-y: auto;
    width: 340px;
}

/* ================= PREVIEW CONTAINER STYLING ================= */

.preview-container {
    width: 652px;
    height: 435px;
    max-width: 100%; /* Responsive on smaller screens */
    aspect-ratio: 3/2; /* 768:512 = 3:2 aspect ratio */
    background-color: #212936; /* Slightly lighter than bg for contrast */
    /* border: 2px dashed #4B5563; */ /* Remove dashed border */
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    position: relative;
    z-index: 0;
    margin: 0 auto; /* Center horizontally */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive scaling while maintaining aspect ratio */
@media (max-width: 820px) {
    .preview-container {
        width: 100%;
        height: auto;
        max-width: 768px;
    }
}

/* Preview wrapper to contain the preview */
.preview-wrapper {
    width: 100%;
    max-width: 768px;
    margin: 0 auto;
}

/* Ensure generated images fit properly */
.preview-container img.generated-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: contain; /* Prevent cropping, show full image */
    display: block;
}

/* Preview workspace section styling */
.preview-workspace-section {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.workspace-header {
    flex-shrink: 0;
}

/* Prompt controls section at bottom */
.prompt-controls-section {
    flex-shrink: 0;
}

/* ================= ACTION AREA STYLING ================= */

.prompt-input-container {
    margin-bottom: 1rem;
}

.prompt-textarea {
    width: 100%;
    min-height: 120px;
    background-color: #374151; /* bg-gray-700 */
    border: 1px solid #4B5563; /* border-gray-600 */
    border-radius: 8px;
    padding: 1rem;
    color: #F9FAFB; /* text-gray-50 */
    font-size: 0.875rem;
    line-height: 1.5;
    resize: vertical;
    transition: all 0.2s ease-in-out;
}

.prompt-textarea:focus {
    outline: none;
    border-color: #8B5CF6; /* border-purple-500 */
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.controls-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    flex-wrap: wrap;
}

.generate-button-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* ================= RIGHT SIDEBAR PLACEHOLDER ================= */

.coming-soon-indicator {
    text-align: center;
    padding: 2rem 1rem;
}

.coming-soon-indicator h3 {
    color: #9CA3AF; /* text-gray-400 */
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.coming-soon-indicator p {
    color: #6B7280; /* text-gray-500 */
    font-size: 0.875rem;
    line-height: 1.4;
}

.coming-soon-icon {
    width: 3rem;
    height: 3rem;
    margin: 0 auto 1rem;
    opacity: 0.5;
    color: #6B7280;
}

/* ================= RESPONSIVE DESIGN ================= */

@media (max-width: 1280px) {
    .app-container {
        grid-template-columns: 378px 1fr 240px;
    }
}

@media (max-width: 1024px) {
    .app-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }
    
    .left-sidebar {
        grid-column: 1;
        grid-row: 1;
        height: auto;
        position: relative;
        border-right: none;
        border-bottom: 1px solid #374151;
        max-height: 40vh;
    }
    
    .main-center-panel {
        grid-column: 1;
        grid-row: 2;
        min-height: auto;
        padding: 1rem;
    }
    
    .right-sidebar {
        display: none; /* Hide on mobile */
    }
    
    .preview-container {
        max-width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 768px) {
    .controls-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .generate-button-container {
        justify-content: center;
    }
    
    .prompt-textarea {
        min-height: 80px;
        font-size: 0.8rem;
    }
}

/* ================= ERROR BANNER (Unchanged) ================= */

.error-banner-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    padding-top: 1rem;
    z-index: 1000;
    pointer-events: none;
}

.error-banner {
    background-color: #C53030;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    opacity: 0;
    transform: translateY(-150%);
    transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
    pointer-events: auto;
    position: relative;
}

.error-banner.show {
    opacity: 1;
    transform: translateY(0);
}

.error-banner-close-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: transparent;
    border: none;
    color: white;
    font-size: 1.5rem;
    line-height: 1;
    cursor: pointer;
    padding: 0.25rem;
}

/* Optional: Add a simple icon if you don't use Heroicons for this */
.error-banner::before {
    /* content: '⚠️'; */ /* Example with emoji */
    /* font-size: 1.25rem; */
}

/* ================= MODAL OVERLAY & POPUP ================= */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(24, 24, 27, 0.7);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-popup {
    z-index: 11;
    position: relative;
    background: #232336;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.45);
    padding: 2rem;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
}

/* ================= INDEPENDENT TOOLTIP SYSTEM ================= */

/* Fixed positioning tooltip that ignores container boundaries */
.tooltip-fixed {
    position: fixed !important;
    z-index: 9999 !important;
    pointer-events: none;
    transform-origin: center;
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

/* Tooltip arrow for fixed tooltips */
.tooltip-arrow-fixed {
    position: absolute;
    width: 8px;
    height: 8px;
    background: inherit;
    z-index: -1;
}

/* Arrow positioning classes */
.tooltip-arrow-top {
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
}

.tooltip-arrow-bottom {
    top: -4px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
}

.tooltip-arrow-left {
    right: -4px;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
}

.tooltip-arrow-right {
    left: -4px;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
}

/* Ensures tooltips stay within viewport bounds */
.tooltip-container {
    position: relative;
    display: inline-block;
}

/* Override any container overflow that might clip tooltips */
.tooltip-no-clip {
    overflow: visible !important;
}

/* Panel preview container with responsive aspect ratio 652x435 */
.panel-preview-container {
    width: 100%;
    max-width: 652px;
    aspect-ratio: 652 / 435;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #181C23;
    border-radius: 16px;
    box-shadow: 0 4px 32px rgba(0,0,0,0.18);
    overflow: hidden;
    margin: 0 auto;
}

@media (max-width: 700px) {
  .panel-preview-container {
    width: 100vw;
    max-width: 100vw;
    aspect-ratio: 652 / 435;
    min-width: 0;
    min-height: 0;
  }
}

/* Enhanced Tab Button Styling - Updated Figma Design Match with Smooth Animation */
.main-tab-navigation-container {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
    width: 324px;
    height: 57px;
    margin-left: auto;
    margin-right: auto;
}

.main-tab-group {
    width: 324px;
    height: 57px;
    background: rgb(57, 65, 80); /* #394150 - matches Figma r: 0.2235, g: 0.2549, b: 0.3137 */
    border-radius: 14px;
    padding: 0;
    display: flex;
    position: relative;
    box-shadow: none;
    border: none;
}

/* Animated highlight background that slides between tabs */
.main-tab-group::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 5px;
    width: 152px; /* 162px - 10px for padding */
    height: 47px; /* 57px - 10px for padding */
    background: rgb(0, 111, 238); /* #006FEE - matches Figma blue */
    border-radius: 12px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    z-index: 1;
    transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(0); /* Default position for first tab */
}

/* Move highlight to second tab when templates is active */
.main-tab-group.templates-active::before {
    transform: translateX(162px); /* Move to second tab position */
}

.main-tab-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px; /* Updated from 12px to 8px per Figma */
    width: 162px; /* Updated from 192.5px to 162px per Figma */
    height: 57px;
    font-size: 16px;
    font-weight: 500;
    transition: color 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: none;
    background: transparent;
    cursor: pointer;
}

/* Remove old ::before pseudo-elements */
.main-tab-button.active-tab {
    background: transparent;
    color: #FFFFFF;
    position: relative;
}

.main-tab-button.active-tab .iconify,
.main-tab-button.active-tab span:not(.iconify) {
    position: relative;
    z-index: 2;
    color: #FFFFFF;
}

.main-tab-button.inactive-tab {
    background: transparent;
    color: rgb(161, 161, 170); /* #A1A1AA - matches Figma r: 0.6314, g: 0.6314, b: 0.6667 */
}

.main-tab-button.inactive-tab .iconify,
.main-tab-button.inactive-tab span:not(.iconify) {
    position: relative;
    z-index: 2;
}

.main-tab-button.inactive-tab:hover {
    color: #F3F4F6;
}

.main-tab-button:focus {
    outline: none;
    box-shadow: none;
}

/* Icon styling within tabs */
.main-tab-button .iconify {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.main-tab-button.active-tab .iconify {
    color: #FFFFFF;
}

.main-tab-button.inactive-tab .iconify {
    color: rgb(161, 161, 170); /* #A1A1AA */
}

.main-tab-button.inactive-tab:hover .iconify {
    color: #F3F4F6;
}

/* First tab positioning */
.main-tab-button:first-child {
    border-radius: 14px 0 0 14px;
}

/* Second tab positioning */
.main-tab-button:last-child {
    border-radius: 0 14px 14px 0;
}

/* Text styling for tab labels */
.main-tab-button span:not(.iconify) {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 500;
    font-size: 16px;
    line-height: 28px;
    letter-spacing: -0.01em;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .main-tab-navigation-container {
        width: 100%;
        max-width: 324px;
        height: 57px;
    }
    
    .main-tab-group {
        width: 100%;
        max-width: 324px;
    }
    
    .main-tab-group::before {
        width: calc(50% - 10px);
    }
    
    .main-tab-group.templates-active::before {
        transform: translateX(calc(50% + 5px));
    }
    
    .main-tab-button {
        width: 50%;
        font-size: 14px;
        gap: 6px;
    }
    
    .main-tab-button .iconify {
        width: 18px;
        height: 18px;
    }
    
    .main-tab-button span:not(.iconify) {
        font-size: 14px;
        line-height: 24px;
    }
}

/* ================= Template Grid Layout - Figma Design Match ================= */

/* Main templates grid - 3x3 layout matching Figma */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* Flexible columns */
    gap: 16px;
    width: 100%;
    max-width: none;
    margin: 0 auto;
    justify-content: center;
}

/* Template category cards */
.template-category-card,
.add-new-card {
    position: relative;
    width: 100%;
    min-height: 140px; /* Make cards taller for better aspect ratio */
    max-width: 100%;
    border-radius: 8px; /* cornerRadius from JSON */
    overflow: hidden;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth easing */
    border: none;
    box-sizing: border-box; /* Ensure proper sizing */
    /* Drop shadows matching Figma JSON */
    box-shadow: 
        0 1px 2px -1px rgba(0, 0, 0, 0.1), /* First shadow: radius 2, offset y:1, spread -1 */
        0 1px 3px 0 rgba(0, 0, 0, 0.1);    /* Second shadow: radius 3, offset y:1, spread 0 */
}

.template-category-card:hover,
.add-new-card:hover {
    transform: translateY(-4px) scale(1.04); /* Lift and scale */
    box-shadow: 
        0 8px 24px rgba(0, 0, 0, 0.18), /* Enhanced shadow for depth */
        0 2px 8px rgba(0, 0, 0, 0.10);
    z-index: 2; /* Ensure hover card appears above others */
}

/* Active selected state */
.template-category-card.active {
    border: 2px solid #006FEE; /* 2px solid border with specified color */
    /* shadow/neutral/md */
    box-shadow: 0px 4px 6px -1px rgba(0, 0, 0, 0.10), 0px 2px 4px -2px rgba(0, 0, 0, 0.10);
}

/* Don't apply hover transform to active cards */
.template-category-card.active:hover {
    transform: none;
    /* Keep the active shadow, don't change it on hover */
    box-shadow: 0px 4px 6px -1px rgba(0, 0, 0, 0.10), 0px 2px 4px -2px rgba(0, 0, 0, 0.10);
}

/* Category card content - full image background */
.template-category-content {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-end;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    box-sizing: border-box; /* Ensure proper sizing */
    /* Fallback background color matching Figma */
    background-color: rgb(248, 72, 72); /* r: 0.9718, g: 0.2832, b: 0.2832 */
}

/* Category label overlay - matching Figma JSON with cropping fix */
.template-category-label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 32px; /* Increased from 27px to prevent cropping */
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 6px 12px; /* Increased padding for better spacing */
    box-sizing: border-box; /* Ensure proper sizing */
    /* Background matching JSON: black with 15% opacity */
    background: rgba(0, 0, 0, 0.15);
    /* Backdrop blur matching JSON: radius 8.5 */
    backdrop-filter: blur(8.5px);
    -webkit-backdrop-filter: blur(8.5px);
    /* Inner shadow effect from JSON */
    box-shadow: inset 0 1px 0 0 rgba(243, 239, 246, 0.04);
    border-radius: 0 0 8px 8px; /* Match parent border radius */
}

.template-category-label span {
    color: white; /* White text from JSON */
    font-size: 14px;
    font-weight: 500;
    text-align: left;
    line-height: 18px; /* Increased line height for better readability */
    width: 100%; /* Use full width instead of fixed 84px */
    white-space: nowrap; /* Prevent text wrapping */
    overflow: hidden; /* Hide overflow */
    text-overflow: ellipsis; /* Show ellipsis for long text */
}

/* Add New card specific styling - Matching updated template card pattern */
.add-new-card {
    /* Inherits all styles from .template-category-card, .add-new-card rule above */
    /* Remove any conflicting individual styles */
}

.add-new-card .template-category-content {
    /* Ensure the content area is properly centered */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 100%;
}

.add-new-card .iconify {
    /* Consistent icon sizing across all breakpoints */
    font-size: 32px !important;
    color: rgb(113, 113, 122);
    margin-bottom: 8px;
    transition: color 0.3s ease, transform 0.3s ease;
}

.add-new-card:hover .iconify {
    /* Subtle hover effect for the icon - works with parent hover transform */
    transform: scale(1.1);
    color: rgb(139, 139, 150); /* Slightly lighter on hover */
}

/* Remove the old template-add-new-content styles since we're using inline styles now */
.template-add-new-content {
    /* This class is no longer used - styling moved to inline for Figma accuracy */
}

/* Responsive adjustments for template grid */
@media (max-width: 900px) {
    .template-category-card,
    .add-new-card {
        min-height: 110px;
    }
    
    .template-category-label {
        min-height: 28px;
        padding: 4px 10px;
    }
    
    .template-category-label span {
        font-size: 13px;
        line-height: 16px;
    }
}

@media (max-width: 640px) {
    .templates-grid {
        grid-template-columns: 1fr; /* Single column on mobile */
        gap: 12px;
    }
    
    .template-category-card,
    .add-new-card {
        min-height: 90px;
    }
    
    .template-category-label {
        min-height: 26px;
        padding: 4px 8px;
    }
    
    .template-category-label span {
        font-size: 12px;
        line-height: 14px;
    }
}

@media (max-width: 480px) {
    .templates-grid {
        grid-template-columns: 1fr; /* Single column for very small screens */
        gap: 10px;
        justify-content: center;
    }
    
    .template-category-card,
    .add-new-card {
        min-height: 100px; /* Slightly larger for better touch targets */
    }
    
    .template-category-card.active {
        border: 2px solid #006FEE;
        box-shadow: 0px 4px 6px -1px rgba(0, 0, 0, 0.10), 0px 2px 4px -2px rgba(0, 0, 0, 0.10);
    }
    
    .template-category-label {
        min-height: 28px;
        padding: 6px 12px;
    }
    
    .template-category-label span {
        font-size: 14px;
        line-height: 16px;
    }
} 