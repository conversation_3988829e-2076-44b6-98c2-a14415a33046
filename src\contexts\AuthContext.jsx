const React = window.React;
const { createContext, useContext, useState, useEffect } = React;
import { supabase } from '../utils/supabaseClient.js';

// Create the auth context
const AuthContext = createContext({});

// Custom hook to use the auth context
export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

// Auth Provider component
export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [session, setSession] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Check active sessions and sets the user
        supabase.auth.getSession().then(({ data: { session } }) => {
            setSession(session);
            setUser(session?.user ?? null);
            setLoading(false);
        });

        const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
            setSession(session);
            setUser(session?.user ?? null);
            setLoading(false);
        });

        return () => subscription.unsubscribe();
    }, []);

    // Sign up with email and password
    const signUp = async (email, password, fullName) => {
        try {
            const { data, error } = await supabase.auth.signUp({
                email,
                password,
                options: {
                    data: {
                        full_name: fullName,
                    }
                }
            });
            
            if (error) throw error;
            
            // Note: User might need to confirm email before being able to login
            return { data, error: null };
        } catch (error) {
            console.error('Signup error:', error);
            return { data: null, error };
        }
    };

    // Sign in with email and password
    const signIn = async (email, password) => {
        try {
            const { data, error } = await supabase.auth.signInWithPassword({
                email,
                password,
            });
            
            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Login error:', error);
            return { data: null, error };
        }
    };

    // Sign in with Google OAuth
    const signInWithGoogle = async () => {
        try {
            const { data, error } = await supabase.auth.signInWithOAuth({
                provider: 'google',
                options: {
                    redirectTo: window.location.origin + '/',
                }
            });
            
            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Google login error:', error);
            return { data: null, error };
        }
    };

    // Sign out
    const signOut = async () => {
        try {
            const { error } = await supabase.auth.signOut();
            if (error) throw error;
            
            // Clear any local storage if needed
            localStorage.removeItem('thumbnail-generator-auth');
            
            return { error: null };
        } catch (error) {
            console.error('Logout error:', error);
            return { error };
        }
    };

    // Reset password
    const resetPassword = async (email) => {
        try {
            const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
                redirectTo: window.location.origin + '/reset-password',
            });
            
            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Password reset error:', error);
            return { data: null, error };
        }
    };

    const value = {
        signUp,
        signIn,
        signInWithGoogle,
        signOut,
        resetPassword,
        user,
        session,
        loading,
    };

    return React.createElement(AuthContext.Provider, { value }, children);
}; 