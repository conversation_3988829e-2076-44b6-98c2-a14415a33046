<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT-4V Thumbnail Generator</title>
    <!-- Tailwind CSS Official CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Hero UI + Tailwind CDN -->
    <link href="https://cdn.heroui.com/v1/tailwind.min.css" rel="stylesheet" />
    <script src="https://cdn.heroui.com/v1/hero-ui.min.js"></script>

    <!-- Iconify Icon Set -->
    <script src="https://code.iconify.design/2/2.2.1/iconify.min.js"></script>

    <!-- Link to custom CSS -->
    <link rel="stylesheet" href="/src/styles/layout.css">
    <link rel="stylesheet" href="/src/styles/controls.css">
    <link rel="stylesheet" href="/src/styles/preview.css">

    <!-- React and Babel for JSX compilation in browser (for MVP/demo) -->
    <script src="https://unpkg.com/react@18/umd/react.development.js" crossorigin></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js" crossorigin></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

</head>
<body class="bg-gray-900 text-gray-100">
    <div id="root"></div>

    <!-- Load React Entry Point -->
    <script type="text/babel" data-type="module" src="/src/Entry.jsx"></script>
</body>
</html> 