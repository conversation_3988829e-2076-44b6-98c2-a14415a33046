/*
 * brandLogos.js
 * Utility helpers for detecting popular tech / design / AI tools in a user prompt.
 * If a brand name is detected, we can instruct the image generator to embed its official logo.
 *
 * NOTE: This is a lightweight list – add more brands as needed. Keywords should be lowercase.
 */

// Categorized brand keywords. Prioritize more specific names.
const GAMING_BRANDS = [
  'fortnite',
  'warzone',
  'call of duty',
  'cod',
  'valorant',
  'pubg',
  'apex legends',
  'apex',
  'cs2',
  'counter strike',
  'cs:go',
  'minecraft', // Added Minecraft as a common game example
  'unity',
  'unreal engine'
];

const TECH_DEV_AI_BRANDS = [
  // AI & Machine Learning (from Branded-logos.md)
  'openai', // (ChatGPT, DALL-E, GPT-4 etc.)
  'chatgpt',
  'dall-e',
  'gpt-4',
  'google ai', // (Gemini, Bard, Vertex AI)
  'gemini',
  'bard',
  'vertex ai',
  'midjourney',
  'stable diffusion',
  'hugging face',
  'tensorflow',
  'pytorch',
  'scikit-learn',
  'keras',
  'jupyter',
  'anaconda',
  'runwayml',
  'synthesia',
  'descript',
  'murf.ai',
  'elevenlabs',
  'notion ai',
  'jasper', // (formerly Jarvis)
  'copy.ai',
  'rytr',
  'writesonic',
  'claude', // (Anthropic)
  'perplexity ai',
  'firebase studio',
  // Existing TECH_DEV_AI_BRANDS (merged and de-duplicated)
  'bolt.new', // Prioritize this specific AI dev tool
  'lovable',  // AI dev tool
  'cursor',   // AI dev tool (also in Branded-logos.md under dev)
  'figma',
  'webflow',
  'framer',
  'photoshop',
  'adobe photoshop',
  'illustrator',
  'adobe illustrator',
  'shopify',
  'canva',
  // 'midjourney', // already in AI
  // 'openai', // already in AI
  // 'chatgpt', // already in AI
  'notion', // also in Productivity
  'github',
  'gitlab',
  'firebase',
  'supabase',
  'vscode',
  'visual studio code',
  'flutter',
  'react',
  'next.js',
  'nextjs',
  'vue',
  'vue.js',
  'angular',
  'node.js',
  'python',
  'aws',
  'amazon web services',
  'google cloud',
  'gcp',
  'azure',
  'microsoft azure',
  'docker',
  'kubernetes',
  'jira',
  'slack',
  'discord',
  // 'zoom', // in Productivity
  'miro',
  'figjam',
  'adobe xd',
  'sketch',
  // 'trello', // in Productivity
  // 'asana', // in Productivity
  // 'clickup', // in Productivity
  'datadog',
  'sentry',
  'postman',
  'vercel',
  'netlify',
  // Design & UX/UI (from Branded-logos.md)
  'invision',
  'marvel',
  'principle',
  'protopie',
  'balsamiq',
  'axure rp',
  'affinity designer',
  'affinity photo',
  'procreate',
  'coreldraw',
  'zeplin',
  'abstract',
  'lottiefiles',
  'wordpress',
  'elementor',
  'squarespace',
  'wix',
  'dorik',
  'carrd',
  'bubble',
  'readymag',
  'tilda publishing',
  // Development & DevOps (from Branded-logos.md)
  'sublime text',
  'atom',
  'jetbrains', // (IntelliJ IDEA, PyCharm, WebStorm, etc.)
  'intellij idea',
  'pycharm',
  'webstorm',
  'bitbucket',
  'confluence',
  'microsoft teams',
  'jenkins',
  'circleci',
  'travis ci',
  'heroku',
  'digitalocean',
  'linode',
  'mongodb',
  'postgresql',
  'mysql',
  'redis',
  'elasticsearch',
  'terraform',
  'ansible',
  'puppet',
  'chef',
  'new relic',
  'insomnia',
  'swagger', // / OpenAPI
  'openapi',
  'graphql',
  'svelte',
  'nuxtjs',
  'gatsby',
  'django',
  'flask',
  'ruby on rails',
  'php',
  'laravel',
  'symfony',
  'java',
  'spring',
  'c#',
  '.net',
  'go',
  'golang',
  'rust',
  'swift',
  'kotlin',
  'react native',
  'electron',
  'zapier',
  'ifttt',
  'make', // (formerly Integromat)
  'integromat',
  'n8n',
  'bolt' // from Branded-logos.md (generic, ensure bolt.new is prioritized by logic)
];

const PRODUCTIVITY_OTHER_BRANDS = [
  'notion', // also in tech/dev
  'obsidian',
  'roam research',
  'airtable',
  'trello',
  'asana',
  'monday.com',
  'clickup',
  // 'miro', // in tech/dev
  // 'figjam', // in tech/dev
  'lucidchart',
  'grammarly',
  'zoom',
  'google meet',
  // 'discord', // in tech/dev
  'loom',
  'adobe premiere pro',
  'final cut pro',
  'davinci resolve',
  'audacity',
  'ableton live',
  'logic pro x'
];

// export const ALL_BRANDS = [...GAMING_BRANDS, ...TECH_DEV_AI_BRANDS]; // Old version
export const ALL_BRANDS = [
  ...new Set([ // Use Set to remove duplicates from combined list
    ...GAMING_BRANDS,
    ...TECH_DEV_AI_BRANDS,
    ...PRODUCTIVITY_OTHER_BRANDS
  ])
];

/**
 * Detect brand keywords in a prompt and categorize them.
 * @param {string} prompt - Raw user prompt.
 * @param {number} [limit=3] - Max number of brands to return to avoid clutter.
 * @returns {{name: string, category: 'game' | 'tech' | 'productivity' | 'other'}[]} Array of matched brand objects.
 */
export function detectBrandLogos(prompt = '', limit = 3) {
  if (!prompt) return [];
  const lower = prompt.toLowerCase();
  const matches = [];

  const addMatch = (name, category = 'other') => {
    const titleCasedName = name.replace(/(^|\s|-)(\w)/g, (_, p1, p2) => p1 + p2.toUpperCase());
    if (matches.length < limit && !matches.some(m => m.name.toLowerCase() === titleCasedName.toLowerCase())) {
      matches.push({ name: titleCasedName, category });
    }
  };

  // Specific checks first for priority brands
  if (lower.includes('bolt.new')) addMatch('Bolt.new', 'tech');
  if (lower.includes('lovable')) addMatch('Lovable', 'tech');
  if (lower.includes('cursor') && (lower.includes('ai') || lower.includes('agent'))) addMatch('Cursor AI', 'tech');

  // Iterate through categorized brands
  for (const keyword of GAMING_BRANDS) {
    if (matches.length >= limit) break;
    if (lower.includes(keyword) && !matches.some(m => m.name.toLowerCase() === keyword.toLowerCase())) {
        addMatch(keyword, 'game');
    }
  }

  for (const keyword of TECH_DEV_AI_BRANDS) {
    if (matches.length >= limit) break;
    if (['bolt.new', 'lovable'].includes(keyword.toLowerCase())) continue; // Already handled
    if (keyword.toLowerCase() === 'cursor' && matches.some(m => m.name === 'Cursor AI')) continue; // Already added as Cursor AI
    if (keyword.toLowerCase() === 'bolt' && matches.some(m => m.name === 'Bolt.new')) continue; // Prefer 'Bolt.new'

    if (lower.includes(keyword)) {
        addMatch(keyword, 'tech');
    }
  }

  for (const keyword of PRODUCTIVITY_OTHER_BRANDS) {
    if (matches.length >= limit) break;
    if (lower.includes(keyword) && !matches.some(m => m.name.toLowerCase() === keyword.toLowerCase())) {
        addMatch(keyword, 'productivity');
    }
  }
  
  // Special handling for 'bolt' if 'bolt.new' wasn't found and 'bolt' is not already added from TECH_DEV_AI_BRANDS
  if (!matches.some(m => m.name.toLowerCase() === 'bolt.new' || m.name.toLowerCase() === 'bolt') && lower.includes('bolt')) {
    addMatch('Bolt', 'tech');
  }

  // Deduplicate based on name (lowercase) and take the first category encountered if names clash after title-casing
  const uniqueMatchesMap = new Map();
  matches.forEach(match => {
    const lcName = match.name.toLowerCase();
    if (!uniqueMatchesMap.has(lcName)) {
        uniqueMatchesMap.set(lcName, match);
    } else {
        // Optional: Prioritize 'tech' or 'game' category if a duplicate is found with 'productivity' or 'other'
        const existingMatch = uniqueMatchesMap.get(lcName);
        if (match.category === 'tech' && existingMatch.category !== 'tech') {
            uniqueMatchesMap.set(lcName, match);
        } else if (match.category === 'game' && existingMatch.category !== 'game' && existingMatch.category !== 'tech') {
            uniqueMatchesMap.set(lcName, match);
        }
    }
  });

  return Array.from(uniqueMatchesMap.values()).slice(0, limit);
}

/**
 * Check if any of the detected brands are gaming brands.
 * @param {{name: string, category: string}[]} detectedBrands - Array of brand objects from detectBrandLogos.
 * @returns {boolean} True if at least one gaming brand is detected.
 */
export function hasGamingBrand(detectedBrands = []) {
    return detectedBrands.some(brand => brand.category === 'game');
}

/**
 * Check if any of the detected brands are tech/dev/ai brands.
 * @param {{name: string, category: string}[]} detectedBrands - Array of brand objects from detectBrandLogos.
 * @returns {boolean} True if at least one tech/dev/ai brand is detected.
 */
export function hasTechBrand(detectedBrands = []) {
    return detectedBrands.some(brand => brand.category === 'tech');
} 