export default [
  {
    id: "vlog-daily",
    name: "[DAY] Daily Vlog!",
    description: "For daily vlogging content.",
    promptBase: "Create a vibrant YouTube thumbnail for '[DAY] Daily Vlog!'. Show a person with a happy expression, camera and sun icons, and bold text overlay: 'DAILY VLOG!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Happy', includeIcons: true, textOverlay: true, overlayText: "DAILY\nVLOG!" },
    templateImagePlaceholder: { text: "Daily Vlog", bgColor: "bg-orange-500" }
  },
  {
    id: "vlog-travel",
    name: "[DESTINATION] Travel Vlog!",
    description: "For travel vlogs and adventures.",
    promptBase: "Design a scenic thumbnail for '[DESTINATION] Travel Vlog!'. Show a person with a backpack, map and airplane icons, and text overlay: 'TRAVEL VLOG!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Curious', includeIcons: true, textOverlay: true, overlayText: "TRAVEL\nVLOG!" },
    templateImagePlaceholder: { text: "Travel Vlog", bgColor: "bg-orange-400" }
  },
  {
    id: "vlog-morning-routine",
    name: "Morning Routine [DAY]!",
    description: "For morning routine vlogs.",
    promptBase: "Create a cozy thumbnail for 'Morning Routine [DAY]!'. Show a person stretching, coffee and clock icons, and text overlay: 'MORNING ROUTINE!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Relaxed', includeIcons: true, textOverlay: true, overlayText: "MORNING\nROUTINE!" },
    templateImagePlaceholder: { text: "Morning Routine", bgColor: "bg-orange-300" }
  },
  {
    id: "vlog-haul",
    name: "[STORE] Haul!",
    description: "For shopping haul vlogs.",
    promptBase: "Design a fun thumbnail for '[STORE] Haul!'. Show a person with shopping bags, gift and tag icons, and bold text overlay: 'HAUL!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Excited', includeIcons: true, textOverlay: true, overlayText: "HAUL!" },
    templateImagePlaceholder: { text: "Haul", bgColor: "bg-orange-200" }
  },
  {
    id: "vlog-qna",
    name: "Q&A With [NAME]",
    description: "For Q&A or interview vlogs.",
    promptBase: "Create a Q&A thumbnail for 'Q&A With [NAME]'. Show two people talking, question and chat icons, and text overlay: 'Q&A'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Talking', includeIcons: true, textOverlay: true, overlayText: "Q&A" },
    templateImagePlaceholder: { text: "Q&A", bgColor: "bg-orange-100" }
  },
  {
    id: "vlog-reaction",
    name: "[TOPIC] Vlog Reaction!",
    description: "For reaction vlogs to events or news.",
    promptBase: "Design a reaction thumbnail for '[TOPIC] Vlog Reaction!'. Show a person with a surprised expression, emoji and news icons, and bold text overlay: 'REACTION!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Shocked', includeIcons: true, textOverlay: true, overlayText: "REACTION!" },
    templateImagePlaceholder: { text: "Vlog Reaction", bgColor: "bg-orange-50" }
  }
]; 