/* Styles specific to the control panel components */
/* (e.g., toggle switches, dropdowns) if needed beyond Tailwind utilities */

.some-control-class {
	/* Example */
 	   /* Mix space and tab */
    border: 1px solid red; /* Example - unlikely needed with Tailwind */
}

/* Dead CSS */
/* .old-toggle-style { color: blue; } */

.bgModal-backdrop {
	z-index: 50 !important;
}

.bgModal-container {
	z-index: 60 !important;
}

/* Template modals should be below background modals */
.template-modal-container {
	z-index: 40 !important;
}

.show-more-modal-container {
	z-index: 40 !important;
}

/* ================= PREMIUM LOADING INDICATOR ================= */

/* Loading indicator container */
.loading-indicator-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
}

/* Loading indicator container for preview panel */
.preview-loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
}

/* Numeric percentage display */
.loading-percentage {
    font-size: 3.5rem;
    font-weight: 900;
    color: #7DD3FC; /* sky-300 (light bluish) */
    text-shadow: 0 0 20px rgba(125, 211, 252, 0.5);
    letter-spacing: -0.02em;
    line-height: 1;
    margin-bottom: 0.5rem;
}

/* Container for the animated text */
.loading-text-container {
    position: relative;
    color: rgba(181, 181, 181, 0.7); /* Base fallback color */
}

/* Cursor thinking text animation */
.cursor-thinking-text {
    font-weight: 500;
    font-size: 1rem;
    color: transparent; /* Crucial for background-clip to work */
    background: linear-gradient(
        90deg,
        rgba(181, 181, 181, 0.85) 20%,
        #FFFFFF 50%,
        rgba(181, 181, 181, 0.85) 80%
    );
    background-size: 200% 100%; /* Ensure gradient is wide enough to move */
    -webkit-background-clip: text;
    background-clip: text;
    animation: cursorThinking 3.4s linear infinite;
}

@keyframes cursorThinking {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 100% 0;
    }
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .loading-percentage {
        font-size: 2.5rem;
    }

    .cursor-thinking-text {
        font-size: 1rem;
    }

    .loading-indicator-container {
        padding: 1.5rem;
        gap: 0.75rem;
    }
}

@media (max-width: 768px) {
    .loading-indicator-container {
        padding: 1.5rem;
        gap: 0.75rem;
    }
}

/* ================= PREVIEW CONTAINER PROGRESS EFFECTS ================= */

/* Preview container loading state */
.preview-container.preview-loading {
    overflow: hidden;
}

/* Subtle glow pulse animation */
@keyframes preview-glow-pulse {
    0%, 100% {
        opacity: 0.6;
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
    }
    50% {
        opacity: 1;
        box-shadow: 0 0 25px rgba(59, 130, 246, 0.4);
    }
}

/* Progress bar with subtle shimmer */
.preview-progress-bar {
    position: relative;
    overflow: hidden;
}

.preview-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: progress-shimmer 2s infinite;
    opacity: 0.7;
}

@keyframes progress-shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Enhanced progress bar visibility */
.preview-progress-overlay {
    backdrop-filter: blur(1px);
}

/* Responsive adjustments for progress effects */
@media (max-width: 640px) {
    .preview-progress-overlay {
        height: 4px;
    }

    .preview-glow-container {
        box-shadow: 0 0 15px rgba(59, 130, 246, 0.25) !important;
    }
}
/* ================= ENHANCED PREMIUM LOADING INDICATOR ================= */
.preview-loading-backdrop { backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px); background: rgba(0, 0, 0, 0.4); animation: backdrop-fade-in 0.3s ease-out; }
@keyframes backdrop-fade-in { from { opacity: 0; backdrop-filter: blur(0px); -webkit-backdrop-filter: blur(0px); } to { opacity: 1; backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px); } }
@keyframes number-pulse { 0%, 100% { transform: scale(1); text-shadow: 0 0 30px rgba(59, 130, 246, 0.6); } 50% { transform: scale(1.05); text-shadow: 0 0 40px rgba(59, 130, 246, 0.8); } }
@supports not (backdrop-filter: blur(8px)) { .preview-loading-backdrop { background: rgba(0, 0, 0, 0.7); } }

/* ================= ENHANCED SOFT BLUISH FEATHER BACKDROP ================= */

/* Soft bluish feather overlay backdrop */
.preview-loading-backdrop {
    position: absolute !important;
    inset: 0 !important;
    z-index: 10 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
    /* Soft blue feathered radial gradient overlay */
    background:
        radial-gradient(ellipse at 50% 50%,
            rgba(59,130,246,0.18) 0%,
            rgba(59,130,246,0.12) 40%,
            rgba(59,130,246,0.06) 70%,
            rgba(59,130,246,0.00) 100%
        ),
        rgba(0,0,0,0.35) !important;
    border-radius: 8px !important;
    animation: enhanced-backdrop-fade-in 0.4s cubic-bezier(0.4,0,0.2,1) !important;
    transition: background 0.4s cubic-bezier(0.4,0,0.2,1) !important;
}

@keyframes enhanced-backdrop-fade-in {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
        -webkit-backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .preview-loading-container {
        padding: 2rem !important;
        gap: 1rem !important;
    }
}
